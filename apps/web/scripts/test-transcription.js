#!/usr/bin/env node

/**
 * Test script for YouTube transcription functionality
 * Usage: node scripts/test-transcription.js [youtube-url] [test-type]
 * 
 * Test types:
 * - audio_fetch: Test only audio fetching
 * - transcription: Test only transcription (requires audio fetch first)
 * - full_pipeline: Test complete pipeline (default)
 */

const https = require('https');
const http = require('http');

// Default test video (short video for quick testing)
const DEFAULT_TEST_URL = 'https://www.youtube.com/watch?v=jNQXAC9IVRw'; // Short accessible video
const API_ENDPOINT = 'http://localhost:3000/api/test-chapters';

async function makeRequest(url, data) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: { error: 'Invalid JSON response', raw: data } });
        }
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.write(postData);
    req.end();
  });
}

function formatDuration(ms) {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  return `${(ms / 60000).toFixed(1)}m`;
}

function formatSize(bytes) {
  if (bytes < 1024) return `${bytes}B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
  return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
}

async function runTest(youtubeUrl, testType) {
  console.log('🧪 YouTube Transcription Test');
  console.log('================================');
  console.log(`📺 Video URL: ${youtubeUrl}`);
  console.log(`🔧 Test Type: ${testType}`);
  console.log(`🌐 API Endpoint: ${API_ENDPOINT}`);
  console.log('');

  const startTime = Date.now();
  
  try {
    console.log('⏳ Starting test...');
    
    const response = await makeRequest(API_ENDPOINT, {
      url: youtubeUrl,
      test_type: testType
    });

    const totalTime = Date.now() - startTime;
    
    console.log(`✅ Test completed in ${formatDuration(totalTime)}`);
    console.log(`📊 HTTP Status: ${response.status}`);
    console.log('');

    if (response.status === 200 && response.data.success) {
      console.log('🎉 Test PASSED');
      console.log('================');
      
      const result = response.data;
      
      if (testType === 'audio_fetch') {
        const audio = result.result;
        console.log(`📁 Filename: ${audio.filename}`);
        console.log(`🎵 Type: ${audio.type}`);
        console.log(`📏 Size: ${formatSize(audio.size_bytes)} (${audio.size_bytes.toLocaleString()} bytes)`);
        console.log(`⏱️  Fetch Duration: ${formatDuration(audio.fetch_duration_ms)}`);
        
      } else if (testType === 'transcription') {
        const trans = result.result;
        console.log(`📝 Text Length: ${trans.text_length.toLocaleString()} characters`);
        console.log(`🔢 Segments: ${trans.segments_count}`);
        console.log(`🌍 Language: ${trans.language || 'unknown'}`);
        console.log(`⏱️  Transcription Duration: ${formatDuration(trans.transcription_duration_ms)}`);
        
        if (trans.first_segment) {
          console.log(`🎬 First Segment: "${trans.first_segment.text}" (${trans.first_segment.start}s - ${trans.first_segment.end}s)`);
        }
        
      } else if (testType === 'full_pipeline') {
        console.log(`⏱️  Total Duration: ${formatDuration(result.total_duration_ms)}`);
        console.log('');
        console.log('📋 Pipeline Steps:');
        
        result.steps.forEach((step, index) => {
          console.log(`  ${index + 1}. ${step.step}: ${step.success ? '✅' : '❌'} (${formatDuration(step.duration_ms)})`);
          
          if (step.step === 'audio_fetch' && step.result) {
            console.log(`     📏 Audio: ${formatSize(step.result.size_bytes)} ${step.result.type}`);
          }
          
          if (step.step === 'transcription' && step.result) {
            console.log(`     📝 Transcription: ${step.result.segments_count} segments, ${step.result.language || 'unknown'} language`);
          }
        });
        
        console.log('');
        console.log('📊 Summary:');
        console.log(`   🆔 Video ID: ${result.summary.video_id}`);
        console.log(`   📏 Audio Size: ${result.summary.audio_size_mb}MB`);
        console.log(`   🔢 Segments: ${result.summary.transcription_segments}`);
        console.log(`   🌍 Language: ${result.summary.detected_language || 'unknown'}`);
      }
      
    } else {
      console.log('❌ Test FAILED');
      console.log('================');
      console.log(`Error: ${response.data.error || 'Unknown error'}`);
      
      if (response.data.steps) {
        console.log('');
        console.log('📋 Completed Steps:');
        response.data.steps.forEach((step, index) => {
          console.log(`  ${index + 1}. ${step.step}: ${step.success ? '✅' : '❌'} (${formatDuration(step.duration_ms)})`);
        });
      }
    }
    
  } catch (error) {
    const totalTime = Date.now() - startTime;
    console.log(`❌ Test FAILED after ${formatDuration(totalTime)}`);
    console.log('================');
    console.log(`Network Error: ${error.message}`);
    console.log('');
    console.log('💡 Make sure the Next.js development server is running:');
    console.log('   npm run dev');
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const youtubeUrl = args[0] || DEFAULT_TEST_URL;
const testType = args[1] || 'full_pipeline';

// Validate test type
const validTestTypes = ['audio_fetch', 'transcription', 'full_pipeline'];
if (!validTestTypes.includes(testType)) {
  console.error(`❌ Invalid test type: ${testType}`);
  console.error(`Valid options: ${validTestTypes.join(', ')}`);
  process.exit(1);
}

// Run the test
runTest(youtubeUrl, testType).catch(console.error);
