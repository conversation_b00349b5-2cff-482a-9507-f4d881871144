# YouTube Transcription Testing

This document explains how to test the YouTube transcription functionality in the application.

## Overview

The transcription system uses Groq's Whisper API to transcribe YouTube videos. The process involves:

1. **Audio Extraction**: Downloading audio from YouTube videos using `ytdl-core` and `play-dl`
2. **Transcription**: Converting audio to text using Groq's Whisper model
3. **Chapter Generation**: Using the transcription to generate video chapters

## Prerequisites

1. **Environment Variables**: Make sure you have `GROQ_API_KEY` set in your environment
2. **Development Server**: The Next.js development server must be running (`npm run dev`)

## Testing Methods

### 1. API Endpoint Testing

The test API endpoint is available at `/api/test-chapters` and supports three test types:

#### Audio Fetch Test
Tests only the audio extraction functionality:
```bash
curl -X POST http://localhost:3000/api/test-chapters \
  -H "Content-Type: application/json" \
  -d '{"url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "test_type": "audio_fetch"}'
```

#### Transcription Test
Tests only the transcription functionality (requires GROQ_API_KEY):
```bash
curl -X POST http://localhost:3000/api/test-chapters \
  -H "Content-Type: application/json" \
  -d '{"url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "test_type": "transcription"}'
```

#### Full Pipeline Test
Tests the complete audio extraction + transcription pipeline:
```bash
curl -X POST http://localhost:3000/api/test-chapters \
  -H "Content-Type: application/json" \
  -d '{"url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "test_type": "full_pipeline"}'
```

### 2. Test Script

A convenient test script is provided for easier testing:

```bash
# Test with default video (Rick Roll - short video)
npm run test:transcription

# Test only audio fetching
npm run test:transcription:audio

# Test full pipeline
npm run test:transcription:full

# Test with custom video
node scripts/test-transcription.js "https://www.youtube.com/watch?v=YOUR_VIDEO_ID" full_pipeline
```

### 3. Manual Testing

You can also test through the main application by:

1. Starting the development server: `npm run dev`
2. Navigate to the application
3. Enter a YouTube URL
4. Monitor the console logs for transcription progress

## Test Output

The test responses include detailed information:

### Audio Fetch Test Response
```json
{
  "success": true,
  "test_type": "audio_fetch",
  "result": {
    "filename": "yt_audio_1234567890.m4a",
    "type": "audio/mp4",
    "size_bytes": 1048576,
    "size_mb": 1.0,
    "fetch_duration_ms": 2500
  }
}
```

### Transcription Test Response
```json
{
  "success": true,
  "test_type": "transcription",
  "result": {
    "text_length": 1250,
    "segments_count": 45,
    "language": "en",
    "transcription_duration_ms": 8500,
    "first_segment": {
      "start": 0.0,
      "end": 2.5,
      "text": "Never gonna give you up"
    },
    "last_segment": {
      "start": 210.0,
      "end": 212.5,
      "text": "Never gonna let you down"
    }
  }
}
```

### Full Pipeline Test Response
```json
{
  "success": true,
  "test_type": "full_pipeline",
  "total_duration_ms": 12000,
  "steps": [
    {
      "step": "audio_fetch",
      "success": true,
      "duration_ms": 2500,
      "result": { /* audio fetch result */ }
    },
    {
      "step": "transcription",
      "success": true,
      "duration_ms": 8500,
      "result": { /* transcription result */ }
    }
  ],
  "summary": {
    "video_id": "dQw4w9WgXcQ",
    "audio_size_mb": 1.0,
    "transcription_segments": 45,
    "detected_language": "en"
  }
}
```

## Troubleshooting

### Common Issues

1. **GROQ_API_KEY not configured**
   - Make sure you have a valid Groq API key
   - Set it in your environment: `export GROQ_API_KEY=your_key_here`

2. **YouTube video not accessible**
   - Some videos may be region-locked or have restricted access
   - Try with a different video URL

3. **Audio extraction fails**
   - YouTube frequently changes their API
   - The fallback mechanisms should handle most cases
   - Check console logs for specific error messages

4. **Large file sizes**
   - Audio files are limited to ~15MB to keep uploads manageable
   - Longer videos will be truncated

### Debug Logs

Enable detailed logging by checking the console output when running tests. Look for:

- `🎬 fetchYouTubeAudioAsFile:start` - Audio extraction started
- `🎧 YouTube audio (play-dl)` - Audio extracted via play-dl
- `🤖 Transcribing YouTube video with Groq` - Transcription started
- `🤖 Groq transcription response summary` - Transcription completed

## Performance Notes

- **Audio Extraction**: Usually takes 1-5 seconds depending on video length and network speed
- **Transcription**: Typically takes 5-15 seconds depending on audio length and Groq API response time
- **Total Pipeline**: Usually completes in 10-30 seconds for short videos (under 5 minutes)

## Test Videos

For consistent testing, use these short videos:

- **Rick Roll**: `https://www.youtube.com/watch?v=dQw4w9WgXcQ` (3:33, English)
- **Short Tech Talk**: Find a 2-3 minute tech video for testing
- **Different Language**: Test with non-English content to verify language detection

## Integration with Main App

The transcription functionality is integrated into the main generation pipeline at `/api/generate`. The test endpoint allows you to isolate and debug transcription issues without running the full chapter generation process.
