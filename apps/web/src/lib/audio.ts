import ytdl from "@distube/ytdl-core";
import { Readable } from "stream";
import * as playdl from "play-dl";

async function streamToBuffer(readable: Readable): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    readable.on("data", (chunk) => chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk)));
    readable.once("end", () => resolve(Buffer.concat(chunks)));
    readable.once("error", reject);
  });
}

export async function fetchYouTubeAudioAsFile(url: string): Promise<File> {
  try {
    console.log('🎬 fetchYouTubeAudioAsFile:start', { url });
  } catch {}
  // 1) Prefer play-dl streaming (more resilient to YouTube changes)
  try {
    const stream = await playdl.stream(url, { quality: 0 });
    const buffer = await streamToBuffer(stream.stream as unknown as Readable);
    const isOpus = stream.type === 'opus';
    const mime = isOpus ? 'audio/ogg' : 'audio/mpeg';
    const ext = isOpus ? 'ogg' : 'mp3';
    const filename = `yt_audio_${Date.now()}.${ext}`;
    try { console.log('🎧 YouTube audio (play-dl)', { mime, ext, bytes: buffer.length }); } catch {}
    return new File([buffer], filename, { type: mime });
  } catch {}

  // 2) Try direct fetch using ytdl-discovered URL, preferring AAC/MP4 (m4a) over webm/opus
  try {
    const info = await ytdl.getInfo(url);
    const audioOnly = info.formats.filter((f: any) => f.hasAudio && !f.hasVideo);
    const mp4Preferred = audioOnly
      .filter((f: any) => (f.mimeType || '').includes('audio/mp4') || (f.container || '').includes('mp4'))
      .sort((a: any, b: any) => (a.bitrate || a.audioBitrate || 0) - (b.bitrate || b.audioBitrate || 0));
    const webmFallback = audioOnly
      .filter((f: any) => (f.mimeType || '').includes('audio/webm') || (f.container || '').includes('webm'))
      .sort((a: any, b: any) => (a.bitrate || a.audioBitrate || 0) - (b.bitrate || b.audioBitrate || 0));
    const candidate = mp4Preferred[0] || webmFallback[0] || ytdl.chooseFormat(info.formats, { quality: "lowestaudio" });
    try { console.log('🎯 ytdl candidate', { mimeType: candidate?.mimeType, container: candidate?.container, hasUrl: Boolean(candidate?.url) }); } catch {}
    if (candidate?.url) {
      const res = await fetch(candidate.url, {
        next: { revalidate: 0 },
        headers: {
          "user-agent":
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0 Safari/537.36",
          referer: "https://www.youtube.com/",
          origin: "https://www.youtube.com",
          // Request only the first 15MB to keep upload small
          Range: "bytes=0-15728639" as any,
        } as any,
      });
      if (!(res.ok || res.status === 206)) {
        try { console.log('⚠️ Direct URL fetch failed', { status: res.status }); } catch {}
        throw new Error(`Failed to fetch audio from YouTube (${res.status})`);
      }
      const buf = await res.arrayBuffer();
      // Decide content type and extension
      const isMp4 = (candidate.mimeType || '').includes('audio/mp4') || (candidate.container || '').includes('mp4');
      const isWebm = (candidate.mimeType || '').includes('audio/webm') || (candidate.container || '').includes('webm');
      const contentType = isMp4 ? 'audio/mp4' : isWebm ? 'audio/webm' : 'audio/mpeg';
      const ext = isMp4 ? 'm4a' : isWebm ? 'webm' : 'mp3';
      const filename = `yt_audio_${info.videoDetails.videoId}.${ext}`;
      try { console.log('🎧 YouTube audio (direct URL)', { contentType, ext, bytes: buf.byteLength, partial: res.status === 206 }); } catch {}
      return new File([buf], filename, { type: contentType });
    }
  } catch {}

  // 3) Final fallback via ytdl streaming
  try {
    const stream = ytdl(url, {
      quality: "lowestaudio",
      requestOptions: {
        headers: {
          "user-agent":
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0 Safari/537.36",
          referer: "https://www.youtube.com/",
          origin: "https://www.youtube.com",
          accept: "*/*",
        },
      },
      highWaterMark: 1 << 25,
      liveBuffer: 4000,
      dlChunkSize: 0,
    });
    const buffer = await streamToBuffer(stream as unknown as Readable);
    const filename = `yt_audio_${Date.now()}.webm`;
    try { console.log('🎧 YouTube audio (ytdl stream)', { mime: 'audio/webm', ext: 'webm', bytes: buffer.length }); } catch {}
    return new File([buffer], filename, { type: "audio/webm" });
  } catch (e) {
    throw e;
  }
}


