import ytdl from "@distube/ytdl-core";
import { Readable } from "stream";
import * as playdl from "play-dl";

// Utility function to generate random user agents
function getRandomUserAgent(): string {
  const userAgents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
  ];
  return userAgents[Math.floor(Math.random() * userAgents.length)];
}

// Utility function to add random delays to avoid rate limiting
async function randomDelay(min: number = 500, max: number = 2000): Promise<void> {
  const delay = Math.floor(Math.random() * (max - min + 1)) + min;
  await new Promise(resolve => setTimeout(resolve, delay));
}

async function streamToBuffer(readable: Readable): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    readable.on("data", (chunk) => chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk)));
    readable.once("end", () => resolve(Buffer.concat(chunks)));
    readable.once("error", reject);
  });
}

export async function fetchYouTubeAudioAsFile(url: string): Promise<File> {
  try {
    console.log('🎬 fetchYouTubeAudioAsFile:start', { url });
  } catch {}

  // Add initial random delay to avoid detection
  await randomDelay(100, 500);

  // 1) Prefer play-dl streaming (more resilient to YouTube changes)
  try {
    console.log('🔄 Attempting play-dl method...');
    const userAgent = getRandomUserAgent();
    console.log('🎭 Using User-Agent:', userAgent.substring(0, 50) + '...');

    const stream = await playdl.stream(url, {
      quality: 0,
      // Add enhanced headers to appear more like a real browser
      requestOptions: {
        headers: {
          'User-Agent': userAgent,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9,es;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'DNT': '1',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'none',
          'Sec-Fetch-User': '?1',
          'Cache-Control': 'max-age=0',
        },
        timeout: 30000,
      }
    });
    const buffer = await streamToBuffer(stream.stream as unknown as Readable);
    const isOpus = stream.type === 'opus';
    const mime = isOpus ? 'audio/ogg' : 'audio/mpeg';
    const ext = isOpus ? 'ogg' : 'mp3';
    const filename = `yt_audio_${Date.now()}.${ext}`;
    try { console.log('🎧 YouTube audio (play-dl)', { mime, ext, bytes: buffer.length }); } catch {}
    return new File([buffer], filename, { type: mime });
  } catch (error: any) {
    console.log('⚠️ play-dl failed:', error?.message);
    await randomDelay(); // Wait before next attempt
  }

  // 2) Try direct fetch using ytdl-discovered URL, preferring AAC/MP4 (m4a) over webm/opus
  try {
    console.log('🔄 Attempting ytdl-core direct URL method...');

    // Enhanced headers to bypass restrictions with random user agent
    const userAgent = getRandomUserAgent();
    console.log('🎭 ytdl using User-Agent:', userAgent.substring(0, 50) + '...');

    const enhancedHeaders = {
      'User-Agent': userAgent,
      'Accept': '*/*',
      'Accept-Language': 'en-US,en;q=0.9,es;q=0.8,fr;q=0.7',
      'Accept-Encoding': 'gzip, deflate, br',
      'Referer': 'https://www.youtube.com/',
      'Origin': 'https://www.youtube.com',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'cross-site',
      'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
      'Sec-Ch-Ua-Mobile': '?0',
      'Sec-Ch-Ua-Platform': '"Windows"',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
    };

    const info = await ytdl.getInfo(url, {
      requestOptions: {
        headers: enhancedHeaders,
        // Add timeout and retry logic
        timeout: 30000,
      }
    });

    const audioOnly = info.formats.filter((f: any) => f.hasAudio && !f.hasVideo);
    const mp4Preferred = audioOnly
      .filter((f: any) => (f.mimeType || '').includes('audio/mp4') || (f.container || '').includes('mp4'))
      .sort((a: any, b: any) => (b.bitrate || b.audioBitrate || 0) - (a.bitrate || a.audioBitrate || 0)); // Sort by highest quality first
    const webmFallback = audioOnly
      .filter((f: any) => (f.mimeType || '').includes('audio/webm') || (f.container || '').includes('webm'))
      .sort((a: any, b: any) => (b.bitrate || b.audioBitrate || 0) - (a.bitrate || a.audioBitrate || 0));
    const candidate = mp4Preferred[0] || webmFallback[0] || ytdl.chooseFormat(info.formats, { quality: "lowestaudio" });

    try { console.log('🎯 ytdl candidate', { mimeType: candidate?.mimeType, container: candidate?.container, hasUrl: Boolean(candidate?.url), bitrate: candidate?.bitrate || candidate?.audioBitrate }); } catch {}

    if (candidate?.url) {
      // Try multiple fetch attempts with different strategies
      const fetchAttempts = [
        // Attempt 1: Standard range request
        {
          headers: {
            ...enhancedHeaders,
            Range: "bytes=0-15728639", // 15MB limit
          }
        },
        // Attempt 2: Without range header
        {
          headers: enhancedHeaders
        },
        // Attempt 3: Minimal headers
        {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://www.youtube.com/',
          }
        }
      ];

      for (let i = 0; i < fetchAttempts.length; i++) {
        try {
          console.log(`🔄 Fetch attempt ${i + 1}/${fetchAttempts.length}`);
          const res = await fetch(candidate.url, {
            next: { revalidate: 0 },
            ...fetchAttempts[i],
          });
          if (res.ok || res.status === 206) {
            const buf = await res.arrayBuffer();
            // Decide content type and extension
            const isMp4 = (candidate.mimeType || '').includes('audio/mp4') || (candidate.container || '').includes('mp4');
            const isWebm = (candidate.mimeType || '').includes('audio/webm') || (candidate.container || '').includes('webm');
            const contentType = isMp4 ? 'audio/mp4' : isWebm ? 'audio/webm' : 'audio/mpeg';
            const ext = isMp4 ? 'm4a' : isWebm ? 'webm' : 'mp3';
            const filename = `yt_audio_${info.videoDetails.videoId}.${ext}`;
            try { console.log('🎧 YouTube audio (direct URL)', { contentType, ext, bytes: buf.byteLength, partial: res.status === 206, attempt: i + 1 }); } catch {}
            return new File([buf], filename, { type: contentType });
          } else {
            console.log(`⚠️ Fetch attempt ${i + 1} failed with status ${res.status}`);
            if (i === fetchAttempts.length - 1) {
              throw new Error(`All fetch attempts failed. Last status: ${res.status}`);
            }
          }
        } catch (fetchError: any) {
          console.log(`⚠️ Fetch attempt ${i + 1} error:`, fetchError.message);
          if (i === fetchAttempts.length - 1) {
            throw fetchError;
          }
          // Wait a bit before next attempt
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }
  } catch (error: any) {
    console.log('⚠️ ytdl-core direct URL method failed:', error?.message);
  }

  // 3) Final fallback via ytdl streaming with enhanced bypass techniques
  try {
    console.log('🔄 Attempting ytdl-core streaming fallback...');

    // Rotate through different user agents and configurations
    const configs = [
      {
        quality: "lowestaudio",
        requestOptions: {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://www.youtube.com/',
            'Origin': 'https://www.youtube.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
          },
          timeout: 30000,
        },
        highWaterMark: 1 << 25,
        liveBuffer: 4000,
        dlChunkSize: 0,
      },
      {
        quality: "lowestaudio",
        requestOptions: {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer': 'https://www.youtube.com/',
            'Accept': '*/*',
          },
          timeout: 20000,
        },
        highWaterMark: 1 << 24,
      },
      {
        quality: "lowestaudio",
        requestOptions: {
          headers: {
            'User-Agent': 'yt-dlp/2023.12.30',
          },
        },
      }
    ];

    for (let i = 0; i < configs.length; i++) {
      try {
        console.log(`🔄 Streaming attempt ${i + 1}/${configs.length}`);
        const stream = ytdl(url, configs[i]);
        const buffer = await streamToBuffer(stream as unknown as Readable);
        const filename = `yt_audio_${Date.now()}.webm`;
        try { console.log('🎧 YouTube audio (ytdl stream)', { mime: 'audio/webm', ext: 'webm', bytes: buffer.length, attempt: i + 1 }); } catch {}
        return new File([buffer], filename, { type: "audio/webm" });
      } catch (streamError: any) {
        console.log(`⚠️ Streaming attempt ${i + 1} failed:`, streamError?.message);
        if (i === configs.length - 1) {
          throw streamError;
        }
        // Wait before next attempt
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  } catch (e: any) {
    console.log('❌ All audio extraction methods failed:', e?.message);
    throw new Error(`Failed to extract audio from YouTube: ${e?.message || 'Unknown error'}`);
  }
}


