/**
 * Advanced YouTube Bypass Techniques
 * This module contains additional methods to bypass YouTube restrictions
 */

// Alternative extraction methods using different approaches
export class YouTubeBypassManager {
  private static readonly PROXY_ENDPOINTS = [
    // Add proxy endpoints if available
    // 'https://proxy1.example.com',
    // 'https://proxy2.example.com',
  ];

  private static readonly ALTERNATIVE_EXTRACTORS = [
    'yt-dlp', // Most powerful extractor
    'youtube-dl', // Classic extractor
    'pytube', // Python-based
  ];

  /**
   * Try extracting using external services (if available)
   */
  static async tryExternalExtraction(videoId: string): Promise<string | null> {
    // Method 1: Try using yt-dlp API endpoints (if you have them)
    try {
      const ytDlpEndpoint = process.env.YT_DLP_API_ENDPOINT;
      if (ytDlpEndpoint) {
        console.log('🔄 Trying yt-dlp API endpoint...');
        const response = await fetch(`${ytDlpEndpoint}/extract`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ url: `https://www.youtube.com/watch?v=${videoId}` })
        });
        
        if (response.ok) {
          const data = await response.json();
          return data.audio_url;
        }
      }
    } catch (error) {
      console.log('⚠️ External extraction failed:', error);
    }

    return null;
  }

  /**
   * Try using YouTube's own API endpoints
   */
  static async tryYouTubeAPI(videoId: string): Promise<string | null> {
    try {
      const apiKey = process.env.YOUTUBE_API_KEY;
      if (!apiKey) return null;

      console.log('🔄 Trying YouTube Data API...');
      
      // Get video details from YouTube API
      const response = await fetch(
        `https://www.googleapis.com/youtube/v3/videos?id=${videoId}&key=${apiKey}&part=snippet,contentDetails,status`
      );
      
      if (response.ok) {
        const data = await response.json();
        const video = data.items?.[0];
        
        if (video && video.status?.uploadStatus === 'processed') {
          console.log('✅ Video is accessible via API');
          // This doesn't give us direct audio URL, but confirms video accessibility
          return 'api_accessible';
        }
      }
    } catch (error) {
      console.log('⚠️ YouTube API check failed:', error);
    }

    return null;
  }

  /**
   * Try using different geographic endpoints
   */
  static async tryGeoBypass(url: string): Promise<any> {
    const geoEndpoints = [
      'https://www.youtube.com',      // Default
      'https://m.youtube.com',        // Mobile
      'https://music.youtube.com',    // Music (sometimes different restrictions)
      'https://gaming.youtube.com',   // Gaming
    ];

    for (const endpoint of geoEndpoints) {
      try {
        console.log(`🌍 Trying geo endpoint: ${endpoint}`);
        const modifiedUrl = url.replace('https://www.youtube.com', endpoint);
        
        // Try with the modified URL
        // This would need to be integrated with your existing extraction logic
        return { success: true, url: modifiedUrl };
      } catch (error) {
        console.log(`⚠️ Geo endpoint ${endpoint} failed:`, error);
      }
    }

    return null;
  }

  /**
   * Generate bypass-optimized headers
   */
  static generateBypassHeaders(attempt: number = 0): Record<string, string> {
    const userAgents = [
      // Desktop browsers
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
      
      // Mobile browsers (sometimes less restricted)
      'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
      'Mozilla/5.0 (Android 14; Mobile; rv:121.0) Gecko/121.0 Firefox/121.0',
      
      // Bot-like but legitimate
      'yt-dlp/2023.12.30',
      'youtube-dl/2021.12.17',
    ];

    const languages = [
      'en-US,en;q=0.9',
      'en-GB,en;q=0.9',
      'es-ES,es;q=0.9,en;q=0.8',
      'fr-FR,fr;q=0.9,en;q=0.8',
      'de-DE,de;q=0.9,en;q=0.8',
    ];

    const userAgent = userAgents[attempt % userAgents.length];
    const language = languages[attempt % languages.length];

    const baseHeaders: Record<string, string> = {
      'User-Agent': userAgent,
      'Accept-Language': language,
      'Accept': '*/*',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1',
      'Connection': 'keep-alive',
    };

    // Add browser-specific headers based on user agent
    if (userAgent.includes('Chrome')) {
      return {
        ...baseHeaders,
        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Sec-Ch-Ua-Mobile': userAgent.includes('Mobile') ? '?1' : '?0',
        'Sec-Ch-Ua-Platform': userAgent.includes('Windows') ? '"Windows"' : userAgent.includes('Mac') ? '"macOS"' : '"Linux"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'cross-site',
      };
    } else if (userAgent.includes('Firefox')) {
      return {
        ...baseHeaders,
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'cross-site',
      };
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      return {
        ...baseHeaders,
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'cross-site',
      };
    }

    return baseHeaders;
  }

  /**
   * Check if video is likely to be restricted
   */
  static async checkVideoAccessibility(videoId: string): Promise<{
    accessible: boolean;
    reason?: string;
    suggestions?: string[];
  }> {
    try {
      // Quick check using YouTube oEmbed API (usually works even for restricted videos)
      const oembedResponse = await fetch(
        `https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${videoId}&format=json`
      );

      if (!oembedResponse.ok) {
        return {
          accessible: false,
          reason: 'Video not found or private',
          suggestions: ['Check if the video URL is correct', 'Video might be private or deleted']
        };
      }

      const oembedData = await oembedResponse.json();
      
      // Check for common restriction indicators
      const title = oembedData.title?.toLowerCase() || '';
      const author = oembedData.author_name?.toLowerCase() || '';
      
      const restrictionIndicators = [
        'fox news', 'cnn', 'bbc', 'nbc', 'abc news', 'cbs',
        'music video', 'official video', 'vevo',
        'live stream', 'premiere'
      ];

      const hasRestrictionIndicators = restrictionIndicators.some(indicator => 
        title.includes(indicator) || author.includes(indicator)
      );

      if (hasRestrictionIndicators) {
        return {
          accessible: false,
          reason: 'Video likely has copyright or geographic restrictions',
          suggestions: [
            'Try using a VPN from a different country',
            'Look for alternative uploads of the same content',
            'Use the video\'s auto-generated captions instead'
          ]
        };
      }

      return { accessible: true };
    } catch (error) {
      return {
        accessible: false,
        reason: 'Unable to check video accessibility',
        suggestions: ['Try the extraction anyway - it might still work']
      };
    }
  }
}

/**
 * Utility functions for bypass techniques
 */
export const BypassUtils = {
  /**
   * Generate random delays to avoid rate limiting
   */
  randomDelay: (min: number = 500, max: number = 3000): Promise<void> => {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    return new Promise(resolve => setTimeout(resolve, delay));
  },

  /**
   * Retry function with exponential backoff
   */
  retryWithBackoff: async <T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> => {
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        if (i === maxRetries - 1) throw error;
        
        const delay = baseDelay * Math.pow(2, i) + Math.random() * 1000;
        console.log(`⏳ Retry ${i + 1}/${maxRetries} in ${Math.round(delay)}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    throw new Error('Max retries exceeded');
  },

  /**
   * Check if error is likely due to restrictions
   */
  isRestrictionError: (error: any): boolean => {
    const message = error?.message?.toLowerCase() || '';
    const restrictionKeywords = [
      '403', 'forbidden', 'restricted', 'unavailable',
      'blocked', 'copyright', 'geographic', 'region'
    ];
    return restrictionKeywords.some(keyword => message.includes(keyword));
  }
};
