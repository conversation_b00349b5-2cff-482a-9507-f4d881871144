/**
 * YouTube Videos Service
 * Fetches videos from user's YouTube account using their OAuth tokens
 */

import { userYouTubeIntegration } from './user-youtube-integration';

export interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  duration: string;
  publishedAt: string;
  viewCount: string;
  url: string;
  channelTitle: string;
}

export interface YouTubeVideosResponse {
  success: boolean;
  videos?: YouTubeVideo[];
  error?: string;
  nextPageToken?: string;
  totalResults?: number;
}

export class YouTubeVideosService {
  /**
   * Fetch user's uploaded videos
   */
  async getUserVideos(options: {
    maxResults?: number;
    pageToken?: string;
    order?: 'date' | 'rating' | 'relevance' | 'title' | 'viewCount';
  } = {}): Promise<YouTubeVideosResponse> {
    try {
      // Get user's YouTube connection
      const connection = await userYouTubeIntegration.getUserConnection();
      
      if (!connection) {
        return {
          success: false,
          error: 'YouTube account not connected'
        };
      }

      // Fetch videos from YouTube API
      const { maxResults = 20, pageToken, order = 'date' } = options;
      
      const searchParams = new URLSearchParams({
        part: 'snippet,contentDetails,statistics',
        forMine: 'true',
        type: 'video',
        maxResults: maxResults.toString(),
        order,
      });

      if (pageToken) {
        searchParams.append('pageToken', pageToken);
      }

      const response = await fetch(
        `https://www.googleapis.com/youtube/v3/search?${searchParams}`,
        {
          headers: {
            'Authorization': `Bearer ${connection.youtubeAccessToken}`,
            'Accept': 'application/json',
          },
        }
      );

      if (!response.ok) {
        // Try to refresh token if unauthorized
        if (response.status === 401) {
          const refreshResult = await this.refreshAccessToken(connection);
          if (refreshResult.success) {
            // Retry with new token
            return this.getUserVideos(options);
          }
        }
        
        throw new Error(`YouTube API error: ${response.status}`);
      }

      const data = await response.json();
      
      // Transform YouTube API response to our format
      const videos: YouTubeVideo[] = data.items?.map((item: any) => ({
        id: item.id.videoId,
        title: item.snippet.title,
        description: item.snippet.description,
        thumbnail: item.snippet.thumbnails?.medium?.url || item.snippet.thumbnails?.default?.url,
        duration: this.formatDuration(item.contentDetails?.duration),
        publishedAt: item.snippet.publishedAt,
        viewCount: item.statistics?.viewCount || '0',
        url: `https://www.youtube.com/watch?v=${item.id.videoId}`,
        channelTitle: item.snippet.channelTitle,
      })) || [];

      return {
        success: true,
        videos,
        nextPageToken: data.nextPageToken,
        totalResults: data.pageInfo?.totalResults || 0,
      };

    } catch (error: any) {
      console.error('❌ Failed to fetch YouTube videos:', error);
      return {
        success: false,
        error: error.message || 'Failed to fetch videos'
      };
    }
  }

  /**
   * Search user's videos
   */
  async searchUserVideos(query: string, options: {
    maxResults?: number;
    pageToken?: string;
  } = {}): Promise<YouTubeVideosResponse> {
    try {
      const connection = await userYouTubeIntegration.getUserConnection();
      
      if (!connection) {
        return {
          success: false,
          error: 'YouTube account not connected'
        };
      }

      const { maxResults = 20, pageToken } = options;
      
      const searchParams = new URLSearchParams({
        part: 'snippet,contentDetails,statistics',
        forMine: 'true',
        type: 'video',
        q: query,
        maxResults: maxResults.toString(),
        order: 'relevance',
      });

      if (pageToken) {
        searchParams.append('pageToken', pageToken);
      }

      const response = await fetch(
        `https://www.googleapis.com/youtube/v3/search?${searchParams}`,
        {
          headers: {
            'Authorization': `Bearer ${connection.youtubeAccessToken}`,
            'Accept': 'application/json',
          },
        }
      );

      if (!response.ok) {
        if (response.status === 401) {
          const refreshResult = await this.refreshAccessToken(connection);
          if (refreshResult.success) {
            return this.searchUserVideos(query, options);
          }
        }
        
        throw new Error(`YouTube API error: ${response.status}`);
      }

      const data = await response.json();
      
      const videos: YouTubeVideo[] = data.items?.map((item: any) => ({
        id: item.id.videoId,
        title: item.snippet.title,
        description: item.snippet.description,
        thumbnail: item.snippet.thumbnails?.medium?.url || item.snippet.thumbnails?.default?.url,
        duration: this.formatDuration(item.contentDetails?.duration),
        publishedAt: item.snippet.publishedAt,
        viewCount: item.statistics?.viewCount || '0',
        url: `https://www.youtube.com/watch?v=${item.id.videoId}`,
        channelTitle: item.snippet.channelTitle,
      })) || [];

      return {
        success: true,
        videos,
        nextPageToken: data.nextPageToken,
        totalResults: data.pageInfo?.totalResults || 0,
      };

    } catch (error: any) {
      console.error('❌ Failed to search YouTube videos:', error);
      return {
        success: false,
        error: error.message || 'Failed to search videos'
      };
    }
  }

  /**
   * Refresh access token
   */
  private async refreshAccessToken(connection: any): Promise<{ success: boolean; error?: string }> {
    try {
      // This would typically call the YouTube integration service to refresh the token
      // For now, return false to indicate refresh failed
      return { success: false, error: 'Token refresh not implemented' };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Format YouTube duration (PT4M13S) to readable format (4:13)
   */
  private formatDuration(duration: string): string {
    if (!duration) return '0:00';
    
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return '0:00';
    
    const hours = parseInt(match[1] || '0');
    const minutes = parseInt(match[2] || '0');
    const seconds = parseInt(match[3] || '0');
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  }
}

export const youtubeVideosService = new YouTubeVideosService();
