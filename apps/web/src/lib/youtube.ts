function parseIso8601Duration(iso: string): number {
  // Example: PT1H2M10S
  const match = /PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/.exec(iso);
  if (!match) return 0;
  const hours = parseInt(match[1] || "0", 10);
  const minutes = parseInt(match[2] || "0", 10);
  const seconds = parseInt(match[3] || "0", 10);
  return hours * 3600 + minutes * 60 + seconds;
}

export async function fetchYouTubeDurationSeconds(videoId: string): Promise<number | null> {
  const apiKey = process.env.YOUTUBE_API_KEY;
  if (!apiKey) return null;
  // Simple in-memory cache with 10 min TTL
  const now = Date.now();
  (globalThis as any).__ytCache = (globalThis as any).__ytCache || new Map<string, { s: number; e: number }>();
  const cache: Map<string, { s: number; e: number }> = (globalThis as any).__ytCache;
  const hit = cache.get(videoId);
  if (hit && hit.e > now) return hit.s;
  const url = `https://www.googleapis.com/youtube/v3/videos?part=contentDetails&id=${encodeURIComponent(
    videoId
  )}&key=${encodeURIComponent(apiKey)}`;
  try {
    const res = await fetch(url, { next: { revalidate: 60 } });
    if (!res.ok) return null;
    const data = (await res.json()) as any;
    const item = data?.items?.[0];
    const iso: string | undefined = item?.contentDetails?.duration;
    if (!iso) return null;
    const seconds = parseIso8601Duration(iso);
    cache.set(videoId, { s: seconds, e: now + 10 * 60 * 1000 });
    return seconds;
  } catch {
    return null;
  }
}

export async function fetchYouTubeSnippet(videoId: string): Promise<{
  title: string;
  description: string;
  channelTitle: string;
} | null> {
  const apiKey = process.env.YOUTUBE_API_KEY;
  if (!apiKey) return null;
  const url = `https://www.googleapis.com/youtube/v3/videos?part=snippet&id=${encodeURIComponent(
    videoId
  )}&key=${encodeURIComponent(apiKey)}`;
  try {
    const res = await fetch(url, { next: { revalidate: 60 } });
    if (!res.ok) return null;
    const data = (await res.json()) as any;
    const item = data?.items?.[0];
    if (!item?.snippet) return null;
    return {
      title: String(item.snippet.title || ''),
      description: String(item.snippet.description || ''),
      channelTitle: String(item.snippet.channelTitle || ''),
    };
  } catch {
    return null;
  }
}

/**
 * Attempt to fetch auto-generated captions from YouTube timedtext endpoint.
 * This works only if captions are available and not restricted.
 */
export async function fetchYouTubeCaptions(
  videoId: string,
  lang: string = 'en'
): Promise<Array<{ start: number; end: number; text: string }> | null> {
  // 1) XML captions
  try {
    const url = `https://video.google.com/timedtext?lang=${encodeURIComponent(lang)}&v=${encodeURIComponent(videoId)}`;
    const res = await fetch(url, { next: { revalidate: 0 } });
    if (res.ok) {
      const xml = await res.text();
      if (xml && xml.includes('<transcript')) {
        const decode = (s: string) =>
          s
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
            .replace(/&#x([0-9A-Fa-f]+);/g, (_, hex) => String.fromCharCode(parseInt(hex, 16)))
            .replace(/&#(\d+);/g, (_, num) => String.fromCharCode(parseInt(num, 10)));

        const segments: Array<{ start: number; end: number; text: string }> = [];
        const re = /<text[^>]*start="([\d.]+)"[^>]*dur="([\d.]+)"[^>]*>([\s\S]*?)<\/text>/g;
        let m: RegExpExecArray | null;
        while ((m = re.exec(xml))) {
          const start = parseFloat(m[1] || '0');
          const dur = parseFloat(m[2] || '0');
          const raw = m[3] || '';
          const text = decode(raw.replace(/\n/g, ' ')).trim();
          if (!text) continue;
          segments.push({ start, end: start + dur, text });
        }
        if (segments.length) return segments;
      }
    }
  } catch {}

  // 2) XML auto-captions (ASR)
  try {
    const url = `https://video.google.com/timedtext?lang=${encodeURIComponent(lang)}&kind=asr&v=${encodeURIComponent(videoId)}`;
    const res = await fetch(url, { next: { revalidate: 0 } });
    if (res.ok) {
      const xml = await res.text();
      if (xml && xml.includes('<transcript')) {
        const decode = (s: string) => s.replace(/&amp;/g, '&').replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&quot;/g, '"').replace(/&#39;/g, "'");
        const segments: Array<{ start: number; end: number; text: string }> = [];
        const re = /<text[^>]*start="([\d.]+)"[^>]*dur="([\d.]+)"[^>]*>([\s\S]*?)<\/text>/g;
        let m: RegExpExecArray | null;
        while ((m = re.exec(xml))) {
          const start = parseFloat(m[1] || '0');
          const dur = parseFloat(m[2] || '0');
          const raw = m[3] || '';
          const text = decode(raw.replace(/\n/g, ' ')).trim();
          if (!text) continue;
          segments.push({ start, end: start + dur, text });
        }
        if (segments.length) return segments;
      }
    }
  } catch {}

  // 3) JSON3 captions
  try {
    const url = `https://video.google.com/timedtext?lang=${encodeURIComponent(lang)}&v=${encodeURIComponent(videoId)}&fmt=json3`;
    const res = await fetch(url, { next: { revalidate: 0 } });
    if (res.ok) {
      const json = await res.json();
      const events = json?.events as Array<any> | undefined;
      if (Array.isArray(events)) {
        const segments: Array<{ start: number; end: number; text: string }> = [];
        for (const ev of events) {
          const startMs = typeof ev.tStartMs === 'number' ? ev.tStartMs : 0;
          const durMs = typeof ev.dDurationMs === 'number' ? ev.dDurationMs : 0;
          const segs = Array.isArray(ev.segs) ? ev.segs : [];
          const text = segs.map((s: any) => s.utf8 || '').join('').trim();
          if (!text) continue;
          const start = Math.max(0, startMs / 1000);
          const end = Math.max(start, (startMs + durMs) / 1000);
          segments.push({ start, end, text });
        }
        if (segments.length) return segments;
      }
    }
  } catch {}

  // 4) JSON3 auto-captions (ASR)
  try {
    const url = `https://video.google.com/timedtext?lang=${encodeURIComponent(lang)}&kind=asr&v=${encodeURIComponent(videoId)}&fmt=json3`;
    const res = await fetch(url, { next: { revalidate: 0 } });
    if (res.ok) {
      const json = await res.json();
      const events = json?.events as Array<any> | undefined;
      if (Array.isArray(events)) {
        const segments: Array<{ start: number; end: number; text: string }> = [];
        for (const ev of events) {
          const startMs = typeof ev.tStartMs === 'number' ? ev.tStartMs : 0;
          const durMs = typeof ev.dDurationMs === 'number' ? ev.dDurationMs : 0;
          const segs = Array.isArray(ev.segs) ? ev.segs : [];
          const text = segs.map((s: any) => s.utf8 || '').join('').trim();
          if (!text) continue;
          const start = Math.max(0, startMs / 1000);
          const end = Math.max(start, (startMs + durMs) / 1000);
          segments.push({ start, end, text });
        }
        if (segments.length) return segments;
      }
    }
  } catch {}

  return null;
}


