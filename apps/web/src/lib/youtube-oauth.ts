/**
 * YouTube OAuth Integration for Enhanced Access
 * Combines OAuth authentication with audio extraction
 */

import { google } from 'googleapis';

export interface YouTubeOAuthConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  accessToken?: string;
  refreshToken?: string;
}

export interface VideoMetadata {
  id: string;
  title: string;
  description: string;
  duration: string;
  channelTitle: string;
  publishedAt: string;
  viewCount: string;
  likeCount?: string;
  categoryId: string;
  tags?: string[];
  defaultLanguage?: string;
  defaultAudioLanguage?: string;
  liveBroadcastContent: string;
  embeddable: boolean;
  regionRestriction?: {
    allowed?: string[];
    blocked?: string[];
  };
  contentRating?: any;
}

export class YouTubeOAuthManager {
  private oauth2Client: any;
  private youtube: any;

  constructor(config: YouTubeOAuthConfig) {
    this.oauth2Client = new google.auth.OAuth2(
      config.clientId,
      config.clientSecret,
      config.redirectUri
    );

    if (config.accessToken) {
      this.oauth2Client.setCredentials({
        access_token: config.accessToken,
        refresh_token: config.refreshToken,
      });
    }

    this.youtube = google.youtube({
      version: 'v3',
      auth: this.oauth2Client,
    });
  }

  /**
   * Generate OAuth URL for user authorization
   */
  getAuthUrl(): string {
    const scopes = [
      'https://www.googleapis.com/auth/youtube.readonly',
      'https://www.googleapis.com/auth/youtube.force-ssl',
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent', // Force refresh token
    });
  }

  /**
   * Exchange authorization code for tokens
   */
  async getTokens(code: string): Promise<{ accessToken: string; refreshToken: string }> {
    const { tokens } = await this.oauth2Client.getToken(code);
    this.oauth2Client.setCredentials(tokens);
    
    return {
      accessToken: tokens.access_token,
      refreshToken: tokens.refresh_token,
    };
  }

  /**
   * Refresh access token
   */
  async refreshAccessToken(): Promise<string> {
    const { credentials } = await this.oauth2Client.refreshAccessToken();
    this.oauth2Client.setCredentials(credentials);
    return credentials.access_token;
  }

  /**
   * Get detailed video metadata using OAuth
   */
  async getVideoMetadata(videoId: string): Promise<VideoMetadata | null> {
    try {
      console.log('🔐 Fetching video metadata with OAuth...');
      
      const response = await this.youtube.videos.list({
        part: [
          'snippet',
          'contentDetails',
          'statistics',
          'status',
          'localizations',
          'recordingDetails'
        ],
        id: [videoId],
      });

      const video = response.data.items?.[0];
      if (!video) {
        console.log('❌ Video not found via OAuth');
        return null;
      }

      const metadata: VideoMetadata = {
        id: video.id,
        title: video.snippet.title,
        description: video.snippet.description,
        duration: video.contentDetails.duration,
        channelTitle: video.snippet.channelTitle,
        publishedAt: video.snippet.publishedAt,
        viewCount: video.statistics.viewCount,
        likeCount: video.statistics.likeCount,
        categoryId: video.snippet.categoryId,
        tags: video.snippet.tags,
        defaultLanguage: video.snippet.defaultLanguage,
        defaultAudioLanguage: video.snippet.defaultAudioLanguage,
        liveBroadcastContent: video.snippet.liveBroadcastContent,
        embeddable: video.status.embeddable,
        regionRestriction: video.contentDetails.regionRestriction,
        contentRating: video.contentDetails.contentRating,
      };

      console.log('✅ OAuth metadata retrieved:', {
        title: metadata.title,
        duration: metadata.duration,
        embeddable: metadata.embeddable,
        hasRegionRestriction: !!metadata.regionRestriction,
      });

      return metadata;
    } catch (error: any) {
      console.log('❌ OAuth metadata fetch failed:', error.message);
      return null;
    }
  }

  /**
   * Check if video is accessible based on OAuth metadata
   */
  async checkVideoAccessibility(videoId: string): Promise<{
    accessible: boolean;
    reason?: string;
    metadata?: VideoMetadata;
    suggestions?: string[];
  }> {
    try {
      const metadata = await this.getVideoMetadata(videoId);
      
      if (!metadata) {
        return {
          accessible: false,
          reason: 'Video not found or private',
          suggestions: ['Check if video ID is correct', 'Video might be private or deleted']
        };
      }

      // Check various restriction indicators
      const restrictions = [];
      const suggestions = [];

      // Check if embeddable
      if (!metadata.embeddable) {
        restrictions.push('Video is not embeddable');
        suggestions.push('Video owner has disabled embedding');
      }

      // Check region restrictions
      if (metadata.regionRestriction) {
        if (metadata.regionRestriction.blocked) {
          restrictions.push(`Blocked in regions: ${metadata.regionRestriction.blocked.join(', ')}`);
          suggestions.push('Try using VPN from allowed region');
        }
        if (metadata.regionRestriction.allowed) {
          restrictions.push(`Only allowed in: ${metadata.regionRestriction.allowed.join(', ')}`);
          suggestions.push('Video has geographic restrictions');
        }
      }

      // Check content rating
      if (metadata.contentRating && Object.keys(metadata.contentRating).length > 0) {
        restrictions.push('Video has content rating restrictions');
        suggestions.push('Age-restricted content may require special handling');
      }

      // Check if it's a live stream
      if (metadata.liveBroadcastContent === 'live') {
        restrictions.push('Live stream content');
        suggestions.push('Live streams require different extraction methods');
      }

      // Check for news/media indicators
      const title = metadata.title.toLowerCase();
      const channel = metadata.channelTitle.toLowerCase();
      const newsIndicators = ['fox news', 'cnn', 'bbc', 'nbc', 'abc news', 'cbs'];
      
      if (newsIndicators.some(indicator => title.includes(indicator) || channel.includes(indicator))) {
        restrictions.push('Major media/news content detected');
        suggestions.push('News content often has strict copyright protection');
      }

      return {
        accessible: restrictions.length === 0,
        reason: restrictions.length > 0 ? restrictions.join('; ') : undefined,
        metadata,
        suggestions: suggestions.length > 0 ? suggestions : undefined,
      };
    } catch (error: any) {
      return {
        accessible: false,
        reason: `OAuth check failed: ${error.message}`,
        suggestions: ['Check OAuth credentials', 'Verify API quotas']
      };
    }
  }

  /**
   * Get video captions using OAuth (alternative to audio extraction)
   */
  async getVideoCaptions(videoId: string, languageCode: string = 'en'): Promise<{
    success: boolean;
    captions?: string;
    language?: string;
    error?: string;
  }> {
    try {
      console.log('🔐 Fetching captions with OAuth...');
      
      // List available captions
      const captionsResponse = await this.youtube.captions.list({
        part: ['snippet'],
        videoId: videoId,
      });

      const captions = captionsResponse.data.items;
      if (!captions || captions.length === 0) {
        return {
          success: false,
          error: 'No captions available for this video'
        };
      }

      // Find caption in requested language or fallback to first available
      let targetCaption = captions.find((cap: any) => 
        cap.snippet.language === languageCode
      );
      
      if (!targetCaption) {
        targetCaption = captions[0]; // Use first available
        console.log(`⚠️ Requested language ${languageCode} not found, using ${targetCaption.snippet.language}`);
      }

      // Download caption content
      const captionContent = await this.youtube.captions.download({
        id: targetCaption.id,
        tfmt: 'srt', // SubRip format
      });

      return {
        success: true,
        captions: captionContent.data,
        language: targetCaption.snippet.language,
      };
    } catch (error: any) {
      console.log('❌ OAuth captions fetch failed:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Enhanced video analysis with OAuth data
   */
  async analyzeVideoForExtraction(videoId: string): Promise<{
    recommendedMethod: 'oauth_captions' | 'standard_extraction' | 'aggressive_bypass';
    confidence: number;
    reasoning: string[];
    metadata?: VideoMetadata;
  }> {
    const accessibility = await this.checkVideoAccessibility(videoId);
    const reasoning = [];
    let recommendedMethod: 'oauth_captions' | 'standard_extraction' | 'aggressive_bypass';
    let confidence = 0;

    if (!accessibility.accessible) {
      reasoning.push(`Video has restrictions: ${accessibility.reason}`);
      
      // Check if captions are available as alternative
      const captionsResult = await this.getVideoCaptions(videoId);
      if (captionsResult.success) {
        recommendedMethod = 'oauth_captions';
        confidence = 0.8;
        reasoning.push('OAuth captions available as alternative to audio extraction');
      } else {
        recommendedMethod = 'aggressive_bypass';
        confidence = 0.3;
        reasoning.push('No captions available, will need aggressive bypass techniques');
      }
    } else {
      recommendedMethod = 'standard_extraction';
      confidence = 0.9;
      reasoning.push('Video appears accessible for standard extraction');
    }

    return {
      recommendedMethod,
      confidence,
      reasoning,
      metadata: accessibility.metadata,
    };
  }
}

/**
 * Utility function to create OAuth manager from environment variables
 */
export function createYouTubeOAuthManager(): YouTubeOAuthManager | null {
  const clientId = process.env.YOUTUBE_OAUTH_CLIENT_ID;
  const clientSecret = process.env.YOUTUBE_OAUTH_CLIENT_SECRET;
  const redirectUri = process.env.YOUTUBE_OAUTH_REDIRECT_URI || 'http://localhost:3000/auth/youtube/callback';

  if (!clientId || !clientSecret) {
    console.log('⚠️ YouTube OAuth credentials not configured');
    return null;
  }

  return new YouTubeOAuthManager({
    clientId,
    clientSecret,
    redirectUri,
    accessToken: process.env.YOUTUBE_OAUTH_ACCESS_TOKEN,
    refreshToken: process.env.YOUTUBE_OAUTH_REFRESH_TOKEN,
  });
}
