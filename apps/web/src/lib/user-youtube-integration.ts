/**
 * User YouTube Integration
 * Manages YouTube OAuth connection for existing Clerk users
 */

import { auth } from "@clerk/nextjs/server";
import { YouTubeOAuthManager } from "./youtube-oauth";

export interface UserYouTubeConnection {
  userId: string; // Clerk user ID
  youtubeAccessToken: string;
  youtubeRefreshToken: string;
  youtubeChannelId?: string;
  youtubeChannelTitle?: string;
  connectedAt: Date;
  lastUsed?: Date;
  isActive: boolean;
}

export class UserYouTubeIntegration {
  private oauthManager: YouTubeOAuthManager;

  constructor() {
    this.oauthManager = new YouTubeOAuthManager({
      clientId: process.env.YOUTUBE_OAUTH_CLIENT_ID!,
      clientSecret: process.env.YOUTUBE_OAUTH_CLIENT_SECRET!,
      redirectUri: process.env.YOUTUBE_OAUTH_REDIRECT_URI || 'http://localhost:3000/auth/youtube/callback',
    });
  }

  /**
   * Generate YouTube OAuth URL for current user
   */
  async generateConnectUrl(): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      const { userId } = auth();
      if (!userId) {
        return { success: false, error: 'User not authenticated' };
      }

      // Add user ID to state parameter for security
      const authUrl = this.oauthManager.getAuthUrl();
      const urlWithState = `${authUrl}&state=${encodeURIComponent(JSON.stringify({ userId }))}`;

      return { success: true, url: urlWithState };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle OAuth callback and store tokens
   */
  async handleCallback(code: string, state: string): Promise<{
    success: boolean;
    connection?: UserYouTubeConnection;
    error?: string;
  }> {
    try {
      // Verify state parameter
      const stateData = JSON.parse(decodeURIComponent(state));
      const { userId } = auth();
      
      if (!userId || stateData.userId !== userId) {
        return { success: false, error: 'Invalid state parameter' };
      }

      // Exchange code for tokens
      const tokens = await this.oauthManager.getTokens(code);
      
      // Get user's YouTube channel info
      this.oauthManager = new YouTubeOAuthManager({
        clientId: process.env.YOUTUBE_OAUTH_CLIENT_ID!,
        clientSecret: process.env.YOUTUBE_OAUTH_CLIENT_SECRET!,
        redirectUri: process.env.YOUTUBE_OAUTH_REDIRECT_URI!,
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
      });

      // Get channel info (optional)
      let channelInfo = null;
      try {
        // This would require additional API call to get channel info
        // For now, we'll skip this to keep it simple
      } catch (error) {
        console.log('Could not fetch channel info:', error);
      }

      const connection: UserYouTubeConnection = {
        userId,
        youtubeAccessToken: tokens.accessToken,
        youtubeRefreshToken: tokens.refreshToken,
        youtubeChannelId: channelInfo?.id,
        youtubeChannelTitle: channelInfo?.title,
        connectedAt: new Date(),
        isActive: true,
      };

      // Store connection (you'll need to implement storage)
      await this.storeUserConnection(connection);

      return { success: true, connection };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user's YouTube connection
   */
  async getUserConnection(): Promise<UserYouTubeConnection | null> {
    try {
      const { userId } = auth();
      if (!userId) return null;

      // Retrieve from storage (implement based on your storage choice)
      return await this.retrieveUserConnection(userId);
    } catch (error) {
      console.log('Error retrieving user connection:', error);
      return null;
    }
  }

  /**
   * Disconnect user's YouTube account
   */
  async disconnectUser(): Promise<{ success: boolean; error?: string }> {
    try {
      const { userId } = auth();
      if (!userId) {
        return { success: false, error: 'User not authenticated' };
      }

      await this.removeUserConnection(userId);
      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Get OAuth manager for authenticated user
   */
  async getOAuthManagerForUser(): Promise<YouTubeOAuthManager | null> {
    const connection = await this.getUserConnection();
    if (!connection || !connection.isActive) return null;

    return new YouTubeOAuthManager({
      clientId: process.env.YOUTUBE_OAUTH_CLIENT_ID!,
      clientSecret: process.env.YOUTUBE_OAUTH_CLIENT_SECRET!,
      redirectUri: process.env.YOUTUBE_OAUTH_REDIRECT_URI!,
      accessToken: connection.youtubeAccessToken,
      refreshToken: connection.youtubeRefreshToken,
    });
  }

  /**
   * Check if current user has YouTube connected
   */
  async isUserConnected(): Promise<boolean> {
    const connection = await this.getUserConnection();
    return connection?.isActive || false;
  }

  /**
   * Refresh user's access token if needed
   */
  async refreshUserToken(): Promise<{ success: boolean; error?: string }> {
    try {
      const connection = await this.getUserConnection();
      if (!connection) {
        return { success: false, error: 'No YouTube connection found' };
      }

      const oauthManager = new YouTubeOAuthManager({
        clientId: process.env.YOUTUBE_OAUTH_CLIENT_ID!,
        clientSecret: process.env.YOUTUBE_OAUTH_CLIENT_SECRET!,
        redirectUri: process.env.YOUTUBE_OAUTH_REDIRECT_URI!,
        refreshToken: connection.youtubeRefreshToken,
      });

      const newAccessToken = await oauthManager.refreshAccessToken();
      
      // Update stored connection
      connection.youtubeAccessToken = newAccessToken;
      connection.lastUsed = new Date();
      await this.storeUserConnection(connection);

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  // Storage methods (implement based on your choice)
  private async storeUserConnection(connection: UserYouTubeConnection): Promise<void> {
    // Option 1: Store in database
    // await db.userYouTubeConnections.upsert({ where: { userId: connection.userId }, data: connection });
    
    // Option 2: Store in memory (for testing)
    // userConnections.set(connection.userId, connection);
    
    // Option 3: Store in Redis
    // await redis.set(`youtube:${connection.userId}`, JSON.stringify(connection));
    
    // For now, we'll use environment variables (not recommended for production)
    console.log('TODO: Implement connection storage for user:', connection.userId);
  }

  private async retrieveUserConnection(userId: string): Promise<UserYouTubeConnection | null> {
    // Implement based on your storage choice
    console.log('TODO: Implement connection retrieval for user:', userId);
    return null;
  }

  private async removeUserConnection(userId: string): Promise<void> {
    // Implement based on your storage choice
    console.log('TODO: Implement connection removal for user:', userId);
  }
}

// Export singleton instance
export const userYouTubeIntegration = new UserYouTubeIntegration();

/**
 * Enhanced transcription service that uses user's YouTube connection
 */
export async function transcribeWithUserAuth(url: string, options: any = {}) {
  const { enhancedTranscriptionService } = await import('./enhanced-transcription');
  
  // Check if user has YouTube connected
  const isConnected = await userYouTubeIntegration.isUserConnected();
  
  if (isConnected) {
    console.log('🔐 Using user\'s YouTube OAuth for enhanced transcription');
    return await enhancedTranscriptionService.transcribeVideo(url, {
      ...options,
      useOAuth: true,
      fallbackToCaptions: true,
    });
  } else {
    console.log('🎵 Using standard transcription (no YouTube OAuth)');
    return await enhancedTranscriptionService.transcribeVideo(url, {
      ...options,
      useOAuth: false,
    });
  }
}

/**
 * Utility to check user's YouTube connection status
 */
export async function getUserYouTubeStatus() {
  const { userId } = auth();
  if (!userId) {
    return { authenticated: false, youtubeConnected: false };
  }

  const isConnected = await userYouTubeIntegration.isUserConnected();
  return {
    authenticated: true,
    youtubeConnected: isConnected,
    userId,
  };
}
