/**
 * Enhanced Transcription Service
 * Combines YouTube OAuth with audio extraction and fallback methods
 */

import { transcribeYouTubeWithGroq } from './transcribe';
import { fetchYouTubeAudioAsFile } from './audio';
import { createYouTubeOAuthManager, YouTubeOAuthManager } from './youtube-oauth';
import { extractYouTubeVideoId } from './url';
import { auth } from "@clerk/nextjs/server";

export interface TranscriptionOptions {
  preferredMethod?: 'audio' | 'captions' | 'auto';
  language?: string;
  useOAuth?: boolean;
  fallbackToCaptions?: boolean;
}

export interface TranscriptionResult {
  success: boolean;
  method: 'groq_audio' | 'oauth_captions' | 'youtube_captions';
  text?: string;
  segments?: Array<{ start: number; end: number; text: string }>;
  language?: string;
  metadata?: any;
  error?: string;
  warnings?: string[];
}

export class EnhancedTranscriptionService {
  private oauthManager: YouTubeOAuthManager | null;

  constructor() {
    this.oauthManager = createYouTubeOAuthManager();
  }

  /**
   * Get OAuth manager for current authenticated user
   */
  private async getUserOAuthManager(): Promise<YouTubeOAuthManager | null> {
    try {
      const { userYouTubeIntegration } = await import('./user-youtube-integration');
      return await userYouTubeIntegration.getOAuthManagerForUser();
    } catch (error) {
      console.log('⚠️ Could not get user OAuth manager:', error);
      return null;
    }
  }

  /**
   * Main transcription method with intelligent fallback
   */
  async transcribeVideo(
    url: string, 
    options: TranscriptionOptions = {}
  ): Promise<TranscriptionResult> {
    const videoId = extractYouTubeVideoId(url);
    if (!videoId) {
      return {
        success: false,
        method: 'groq_audio',
        error: 'Invalid YouTube URL'
      };
    }

    console.log('🎯 Starting enhanced transcription for:', videoId);
    const warnings: string[] = [];

    // Step 1: Analyze video with OAuth if available
    let analysisResult = null;
    let userOAuthManager = null;

    if (options.useOAuth !== false) {
      // Try to get user's OAuth manager first
      userOAuthManager = await this.getUserOAuthManager();

      if (userOAuthManager) {
        console.log('🔐 Using user\'s YouTube OAuth for analysis');
        try {
          analysisResult = await userOAuthManager.analyzeVideoForExtraction(videoId);
          console.log('📊 User OAuth analysis:', {
            method: analysisResult.recommendedMethod,
            confidence: analysisResult.confidence,
            reasoning: analysisResult.reasoning
          });
        } catch (error: any) {
          warnings.push(`User OAuth analysis failed: ${error.message}`);
          console.log('⚠️ User OAuth analysis failed, trying fallback');
        }
      }

      // Fallback to system OAuth if user OAuth not available
      if (!analysisResult && this.oauthManager) {
        console.log('🔄 Falling back to system OAuth for analysis');
        try {
          analysisResult = await this.oauthManager.analyzeVideoForExtraction(videoId);
          console.log('📊 System OAuth analysis:', {
            method: analysisResult.recommendedMethod,
            confidence: analysisResult.confidence,
            reasoning: analysisResult.reasoning
          });
        } catch (error: any) {
          warnings.push(`System OAuth analysis failed: ${error.message}`);
          console.log('⚠️ System OAuth analysis failed, proceeding without it');
        }
      }
    }

    // Step 2: Choose transcription method based on analysis and preferences
    const method = this.determineTranscriptionMethod(options, analysisResult);
    console.log('🎯 Selected transcription method:', method);

    // Step 3: Execute transcription with fallbacks
    switch (method) {
      case 'oauth_captions':
        return await this.transcribeWithOAuthCaptions(videoId, options, warnings, userOAuthManager);

      case 'audio_extraction':
        return await this.transcribeWithAudioExtraction(url, videoId, options, warnings);

      case 'hybrid':
        return await this.transcribeWithHybridApproach(url, videoId, options, warnings, userOAuthManager);
      
      default:
        return await this.transcribeWithAudioExtraction(url, videoId, options, warnings);
    }
  }

  /**
   * Determine best transcription method based on analysis
   */
  private determineTranscriptionMethod(
    options: TranscriptionOptions,
    analysis: any
  ): 'oauth_captions' | 'audio_extraction' | 'hybrid' {
    // User preference override
    if (options.preferredMethod === 'captions') {
      return 'oauth_captions';
    }
    if (options.preferredMethod === 'audio') {
      return 'audio_extraction';
    }

    // OAuth analysis recommendation
    if (analysis) {
      if (analysis.recommendedMethod === 'oauth_captions') {
        return 'oauth_captions';
      }
      if (analysis.recommendedMethod === 'aggressive_bypass' && options.fallbackToCaptions) {
        return 'hybrid';
      }
    }

    // Default to audio extraction
    return 'audio_extraction';
  }

  /**
   * Transcribe using OAuth captions
   */
  private async transcribeWithOAuthCaptions(
    videoId: string,
    options: TranscriptionOptions,
    warnings: string[],
    userOAuthManager?: YouTubeOAuthManager | null
  ): Promise<TranscriptionResult> {
    const oauthManager = userOAuthManager || this.oauthManager;

    if (!oauthManager) {
      return {
        success: false,
        method: 'oauth_captions',
        error: 'OAuth not configured',
        warnings
      };
    }

    try {
      const managerType = userOAuthManager ? 'user OAuth' : 'system OAuth';
      console.log(`🔐 Attempting ${managerType} captions extraction...`);
      const captionsResult = await oauthManager.getVideoCaptions(
        videoId,
        options.language || 'en'
      );

      if (!captionsResult.success) {
        return {
          success: false,
          method: 'oauth_captions',
          error: captionsResult.error,
          warnings
        };
      }

      // Parse SRT captions into segments
      const segments = this.parseSRTCaptions(captionsResult.captions!);
      const text = segments.map(s => s.text).join(' ');

      console.log('✅ OAuth captions extracted successfully');
      return {
        success: true,
        method: 'oauth_captions',
        text,
        segments,
        language: captionsResult.language,
        warnings
      };
    } catch (error: any) {
      return {
        success: false,
        method: 'oauth_captions',
        error: error.message,
        warnings
      };
    }
  }

  /**
   * Transcribe using audio extraction + Groq
   */
  private async transcribeWithAudioExtraction(
    url: string,
    videoId: string,
    options: TranscriptionOptions,
    warnings: string[]
  ): Promise<TranscriptionResult> {
    try {
      console.log('🎵 Attempting audio extraction + Groq transcription...');
      const result = await transcribeYouTubeWithGroq(videoId);

      if (!result) {
        return {
          success: false,
          method: 'groq_audio',
          error: 'Groq transcription failed',
          warnings
        };
      }

      console.log('✅ Audio transcription completed successfully');
      return {
        success: true,
        method: 'groq_audio',
        text: result.text,
        segments: result.segments,
        language: result.language,
        warnings
      };
    } catch (error: any) {
      return {
        success: false,
        method: 'groq_audio',
        error: error.message,
        warnings
      };
    }
  }

  /**
   * Hybrid approach: Try audio first, fallback to captions
   */
  private async transcribeWithHybridApproach(
    url: string,
    videoId: string,
    options: TranscriptionOptions,
    warnings: string[],
    userOAuthManager?: YouTubeOAuthManager | null
  ): Promise<TranscriptionResult> {
    console.log('🔄 Attempting hybrid transcription approach...');

    // Try audio extraction first
    const audioResult = await this.transcribeWithAudioExtraction(url, videoId, options, warnings);
    if (audioResult.success) {
      return audioResult;
    }

    warnings.push(`Audio extraction failed: ${audioResult.error}`);
    console.log('⚠️ Audio extraction failed, falling back to OAuth captions...');

    // Fallback to OAuth captions
    const captionsResult = await this.transcribeWithOAuthCaptions(videoId, options, warnings, userOAuthManager);
    if (captionsResult.success) {
      return captionsResult;
    }

    // Both methods failed
    return {
      success: false,
      method: 'groq_audio',
      error: 'Both audio extraction and OAuth captions failed',
      warnings
    };
  }

  /**
   * Parse SRT caption format into segments
   */
  private parseSRTCaptions(srtContent: string): Array<{ start: number; end: number; text: string }> {
    const segments: Array<{ start: number; end: number; text: string }> = [];
    const blocks = srtContent.split('\n\n').filter(block => block.trim());

    for (const block of blocks) {
      const lines = block.split('\n');
      if (lines.length < 3) continue;

      const timeMatch = lines[1].match(/(\d{2}):(\d{2}):(\d{2}),(\d{3}) --> (\d{2}):(\d{2}):(\d{2}),(\d{3})/);
      if (!timeMatch) continue;

      const startTime = this.parseTimeToSeconds(timeMatch[1], timeMatch[2], timeMatch[3], timeMatch[4]);
      const endTime = this.parseTimeToSeconds(timeMatch[5], timeMatch[6], timeMatch[7], timeMatch[8]);
      const text = lines.slice(2).join(' ').replace(/<[^>]*>/g, ''); // Remove HTML tags

      segments.push({
        start: startTime,
        end: endTime,
        text: text.trim()
      });
    }

    return segments;
  }

  /**
   * Convert SRT time format to seconds
   */
  private parseTimeToSeconds(hours: string, minutes: string, seconds: string, milliseconds: string): number {
    return parseInt(hours) * 3600 + 
           parseInt(minutes) * 60 + 
           parseInt(seconds) + 
           parseInt(milliseconds) / 1000;
  }

  /**
   * Get video accessibility info using OAuth
   */
  async getVideoAccessibilityInfo(url: string): Promise<any> {
    const videoId = extractYouTubeVideoId(url);
    if (!videoId || !this.oauthManager) {
      return null;
    }

    try {
      return await this.oauthManager.checkVideoAccessibility(videoId);
    } catch (error) {
      console.log('⚠️ Failed to get accessibility info:', error);
      return null;
    }
  }

  /**
   * Check if OAuth is configured and working
   */
  async checkOAuthStatus(): Promise<{
    configured: boolean;
    authenticated: boolean;
    error?: string;
  }> {
    if (!this.oauthManager) {
      return {
        configured: false,
        authenticated: false,
        error: 'OAuth credentials not configured'
      };
    }

    try {
      // Try a simple API call to test authentication
      await this.oauthManager.getVideoMetadata('dQw4w9WgXcQ'); // Test with Rick Roll
      return {
        configured: true,
        authenticated: true
      };
    } catch (error: any) {
      return {
        configured: true,
        authenticated: false,
        error: error.message
      };
    }
  }
}

// Export singleton instance
export const enhancedTranscriptionService = new EnhancedTranscriptionService();
