// Groq Whisper transcription helper (best-effort; optional at runtime)
// Requires: GROQ_API_KEY and a direct-accessible audio URL (e.g., mp3/mp4/m4a)
import { fetchYouTubeAudioAsFile } from "@/lib/audio";
export type TranscriptSegment = {
  start: number; // seconds
  end: number; // seconds
  text: string;
};

export type TranscriptionResult = {
  text: string;
  segments?: TranscriptSegment[];
  language?: string;
};

export async function fetchAudioAsFile(audioUrl: string): Promise<File> {
  const res = await fetch(audioUrl);
  if (!res.ok) throw new Error(`Failed to fetch audio (${res.status})`);
  const buf = await res.arrayBuffer();
  const contentType = res.headers.get("content-type") || "audio/mpeg";
  const filename = `audio_${Date.now()}.bin`;
  return new File([buf], filename, { type: contentType });
}

export async function transcribeWithGroqFromUrl(audioUrl: string): Promise<TranscriptionResult | null> {
  const apiKey = process.env.GROQ_API_KEY;
  if (!apiKey) {
    console.error('❌ GROQ_API_KEY missing');
    return null;
  }
  try {
    const file = await fetchAudioAsFile(audioUrl);
    // Dynamic import supported in server context
    const mod = await import("groq-sdk");
    const Groq = (mod as any).default || (mod as any);
    const groq = new Groq({ apiKey });
    // Use Whisper-large-v3 (or v2); ask for verbose JSON if supported
    const resp = await (groq as any).audio.transcriptions.create({
      file,
      model: process.env.GROQ_WHISPER_MODEL || "whisper-large-v3",
      response_format: "verbose_json",
    });
    // Try to normalize response
    const text: string = resp?.text || "";
    const segments: TranscriptSegment[] | undefined = resp?.segments?.map((s: any) => ({
      start: Number(s.start ?? 0),
      end: Number(s.end ?? 0),
      text: String(s.text ?? ""),
    }));
    const language: string | undefined = resp?.language || resp?.detected_language || undefined;
    console.log('🤖 Groq transcription response:', { text, segments, language });
    return { text, segments, language };
  } catch (error: any) {
    console.error('❌ transcribeWithGroqFromUrl error:', error?.message || error);
    return null;
  }
}


/**
 * Helper to transcribe YouTube audio by videoId using Groq Whisper
 */
export async function transcribeYouTubeWithGroq(videoId: string): Promise<TranscriptionResult | null> {
  const apiKey = process.env.GROQ_API_KEY;
  console.log('🤖 Transcribing YouTube video with Groq:', videoId);
  if (!apiKey) {
    console.error('❌ GROQ_API_KEY missing');
    return null;
  }
  try {
    const watchUrl = `https://www.youtube.com/watch?v=${videoId}`;
    const file = await fetchYouTubeAudioAsFile(watchUrl);
    console.log('🎧 Prepared audio file for Groq:', {
      name: file?.name,
      type: file?.type,
      size_bytes: file?.size,
    });
    const mod = await import("groq-sdk");
    const Groq = (mod as any).default || (mod as any);
    const groq = new Groq({ apiKey });
    const model = process.env.GROQ_WHISPER_MODEL || "whisper-large-v3";
    console.log('🎙️ Using Groq Whisper model:', model);
    const resp = await (groq as any).audio.transcriptions.create({
      file,
      model,
      response_format: "verbose_json",
    });
    const text: string = resp?.text || "";
    const segments: TranscriptSegment[] | undefined = resp?.segments?.map((s: any) => ({
      start: Number(s.start ?? 0),
      end: Number(s.end ?? 0),
      text: String(s.text ?? ""),
    }));
    const language: string | undefined = resp?.language || resp?.detected_language || undefined;
    console.log('🤖 Groq transcription response summary:', { textLength: text.length, segments: segments?.length || 0, language });
    return { text, segments, language };
  } catch (error: any) {
    console.error('❌ transcribeYouTubeWithGroq error:', error?.message || error);
    return null;
  }
}


