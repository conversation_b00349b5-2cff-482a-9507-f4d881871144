"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Youtube, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

interface YouTubeConnectionStatus {
  connected: boolean;
  userId?: string;
  error?: string;
}

export function YouTubeConnection() {
  const [status, setStatus] = useState<YouTubeConnectionStatus>({ connected: false });
  const [loading, setLoading] = useState(true);
  const [connecting, setConnecting] = useState(false);

  useEffect(() => {
    checkConnectionStatus();
  }, []);

  const checkConnectionStatus = async () => {
    try {
      const response = await fetch('/api/auth/youtube/connect', {
        method: 'POST',
      });
      const data = await response.json();
      
      if (data.success) {
        setStatus({ connected: data.connected, userId: data.userId });
      } else {
        setStatus({ connected: false, error: data.error });
      }
    } catch (error) {
      setStatus({ connected: false, error: 'Failed to check connection status' });
    } finally {
      setLoading(false);
    }
  };

  const handleConnect = async () => {
    setConnecting(true);
    try {
      const response = await fetch('/api/auth/youtube/connect');
      const data = await response.json();
      
      if (data.success && data.authUrl) {
        // Redirect to YouTube OAuth
        window.location.href = data.authUrl;
      } else {
        setStatus({ connected: false, error: data.error || 'Failed to generate OAuth URL' });
      }
    } catch (error) {
      setStatus({ connected: false, error: 'Failed to initiate connection' });
    } finally {
      setConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    setConnecting(true);
    try {
      const response = await fetch('/api/auth/youtube/disconnect', {
        method: 'POST',
      });
      const data = await response.json();
      
      if (data.success) {
        setStatus({ connected: false });
      } else {
        setStatus({ ...status, error: data.error || 'Failed to disconnect' });
      }
    } catch (error) {
      setStatus({ ...status, error: 'Failed to disconnect' });
    } finally {
      setConnecting(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Checking YouTube connection...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Youtube className="h-5 w-5 text-red-600" />
          YouTube Integration
        </CardTitle>
        <CardDescription>
          Connect your YouTube account for enhanced transcription features and better success rates with restricted content.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Connection Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="font-medium">Status:</span>
            {status.connected ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Connected
              </Badge>
            ) : (
              <Badge variant="secondary">
                <AlertCircle className="h-3 w-3 mr-1" />
                Not Connected
              </Badge>
            )}
          </div>
        </div>

        {/* Error Message */}
        {status.error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{status.error}</p>
          </div>
        )}

        {/* Benefits */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Benefits of connecting YouTube:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Higher success rates for restricted content</li>
            <li>• Access to video captions as fallback</li>
            <li>• Better error diagnostics</li>
            <li>• Support for private/unlisted videos you own</li>
          </ul>
        </div>

        {/* Action Button */}
        <div className="pt-2">
          {status.connected ? (
            <Button 
              variant="outline" 
              onClick={handleDisconnect}
              disabled={connecting}
              className="w-full"
            >
              {connecting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Disconnecting...
                </>
              ) : (
                'Disconnect YouTube Account'
              )}
            </Button>
          ) : (
            <Button 
              onClick={handleConnect}
              disabled={connecting}
              className="w-full"
            >
              {connecting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Connecting...
                </>
              ) : (
                <>
                  <Youtube className="h-4 w-4 mr-2" />
                  Connect YouTube Account
                </>
              )}
            </Button>
          )}
        </div>

        {/* Note */}
        <p className="text-xs text-gray-500">
          Note: YouTube connection is optional. The app works without it, but connecting provides enhanced features for better transcription success rates.
        </p>
      </CardContent>
    </Card>
  );
}
