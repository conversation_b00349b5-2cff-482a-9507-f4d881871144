"use client";

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Play, Clock, Eye, Calendar, Loader2 } from 'lucide-react';

interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  duration: string;
  publishedAt: string;
  viewCount: string;
  url: string;
  channelTitle: string;
}

interface YouTubeVideoSelectorProps {
  onVideoSelect: (url: string) => void;
  onUrlChange: (url: string) => void;
  currentUrl: string;
  disabled?: boolean;
}

export function YouTubeVideoSelector({ 
  onVideoSelect, 
  onUrlChange, 
  currentUrl, 
  disabled = false 
}: YouTubeVideoSelectorProps) {
  const { user, isLoaded, isSignedIn } = useUser();
  const [videos, setVideos] = useState<YouTubeVideo[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchLoading, setSearchLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [youtubeConnected, setYoutubeConnected] = useState(false);

  // Check YouTube connection status
  useEffect(() => {
    if (!isLoaded || !isSignedIn) return;
    
    const checkConnection = async () => {
      try {
        const response = await fetch('/api/youtube-oauth/connect', {
          method: 'POST',
        });
        const data = await response.json();
        if (data.success) {
          setYoutubeConnected(data.connected);
          if (data.connected) {
            loadUserVideos();
          }
        }
      } catch (error) {
        console.log('Could not check YouTube connection:', error);
      }
    };

    checkConnection();
  }, [isLoaded, isSignedIn]);

  const loadUserVideos = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/youtube-oauth/videos?maxResults=12&order=date');
      const data = await response.json();
      
      if (data.success) {
        setVideos(data.videos || []);
      } else {
        setError(data.error || 'Failed to load videos');
      }
    } catch (error: any) {
      setError('Failed to load videos');
      console.error('Error loading videos:', error);
    } finally {
      setLoading(false);
    }
  };

  const searchVideos = async () => {
    if (!searchQuery.trim()) {
      loadUserVideos();
      return;
    }

    setSearchLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/youtube-oauth/videos?q=${encodeURIComponent(searchQuery)}&maxResults=12`);
      const data = await response.json();
      
      if (data.success) {
        setVideos(data.videos || []);
      } else {
        setError(data.error || 'Failed to search videos');
      }
    } catch (error: any) {
      setError('Failed to search videos');
      console.error('Error searching videos:', error);
    } finally {
      setSearchLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatViewCount = (count: string) => {
    const num = parseInt(count);
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return count;
  };

  if (!isLoaded) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Play className="h-5 w-5" />
          Select Video
        </CardTitle>
        <CardDescription>
          Choose a video from your YouTube channel or paste a URL
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="url" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="url">Paste URL</TabsTrigger>
            <TabsTrigger value="select" disabled={!youtubeConnected}>
              My Videos {!youtubeConnected && '(Connect YouTube)'}
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="url" className="space-y-4">
            <div className="space-y-2">
              <Input
                placeholder="https://www.youtube.com/watch?v=VIDEO_ID"
                value={currentUrl}
                onChange={(e) => onUrlChange(e.target.value)}
                disabled={disabled}
                className="w-full"
              />
              <p className="text-sm text-gray-600">
                Paste any YouTube video URL to generate chapters
              </p>
            </div>
          </TabsContent>
          
          <TabsContent value="select" className="space-y-4">
            {!youtubeConnected ? (
              <div className="text-center py-8">
                <p className="text-gray-600 mb-4">
                  Connect your YouTube account to select from your videos
                </p>
                <Button asChild>
                  <a href="/account">Connect YouTube Account</a>
                </Button>
              </div>
            ) : (
              <>
                {/* Search */}
                <div className="flex gap-2">
                  <Input
                    placeholder="Search your videos..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && searchVideos()}
                    disabled={disabled}
                  />
                  <Button 
                    onClick={searchVideos} 
                    disabled={disabled || searchLoading}
                    size="icon"
                  >
                    {searchLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Search className="h-4 w-4" />
                    )}
                  </Button>
                </div>

                {/* Error */}
                {error && (
                  <div className="text-red-600 text-sm bg-red-50 p-3 rounded">
                    {error}
                  </div>
                )}

                {/* Loading */}
                {loading && (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin" />
                    <span className="ml-2">Loading your videos...</span>
                  </div>
                )}

                {/* Videos Grid */}
                {!loading && videos.length > 0 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                    {videos.map((video) => (
                      <Card 
                        key={video.id} 
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          currentUrl === video.url ? 'ring-2 ring-blue-500' : ''
                        }`}
                        onClick={() => {
                          onVideoSelect(video.url);
                          onUrlChange(video.url);
                        }}
                      >
                        <div className="relative">
                          <img 
                            src={video.thumbnail} 
                            alt={video.title}
                            className="w-full h-32 object-cover rounded-t"
                          />
                          <Badge className="absolute bottom-2 right-2 bg-black/70 text-white">
                            {video.duration}
                          </Badge>
                        </div>
                        <CardContent className="p-3">
                          <h4 className="font-medium text-sm line-clamp-2 mb-2">
                            {video.title}
                          </h4>
                          <div className="flex items-center gap-4 text-xs text-gray-600">
                            <div className="flex items-center gap-1">
                              <Eye className="h-3 w-3" />
                              {formatViewCount(video.viewCount)}
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {formatDate(video.publishedAt)}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}

                {/* No videos */}
                {!loading && videos.length === 0 && !error && (
                  <div className="text-center py-8 text-gray-600">
                    {searchQuery ? 'No videos found for your search.' : 'No videos found in your channel.'}
                  </div>
                )}
              </>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
