"use client";
import Link from "next/link";
import { use<PERSON><PERSON>, SignIn<PERSON>utton, SignUp<PERSON>utton, UserButton } from "@clerk/nextjs";
import { Button } from "@/components/ui/button";

export function HeaderAuth() {
  const { isLoaded, isSignedIn, user } = useUser();

  if (!isLoaded) {
    return (
      <div className="flex items-center gap-2">
        <div className="w-16 h-8 bg-gray-200 animate-pulse rounded"></div>
      </div>
    );
  }

  if (!isSignedIn) {
    return (
      <div className="flex items-center gap-2">
        <SignInButton mode="modal">
          <Button size="sm" variant="ghost">Sign in</Button>
        </SignInButton>
        <SignUpButton mode="modal">
          <Button size="sm">Sign up</Button>
        </SignUpButton>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-gray-600 hidden sm:inline">
        {user.emailAddresses[0]?.emailAddress}
      </span>
      <Button asChild size="sm" variant="ghost">
        <Link href="/account">Account</Link>
      </Button>
      <UserButton 
        appearance={{
          elements: {
            avatarBox: "w-8 h-8"
          }
        }}
        afterSignOutUrl="/"
      />
    </div>
  );
}