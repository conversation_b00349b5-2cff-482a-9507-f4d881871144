import type { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import { Clerk<PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import "./globals.css";
import { HeaderAuth } from "@/components/header-auth";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ChapterGen — YouTube Chapter Generator",
  description: "Generate timestamped chapters for YouTube videos.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
          <header className="border-b bg-white/70 dark:bg-black/30 backdrop-blur sticky top-0 z-40">
            <div className="max-w-6xl mx-auto px-4 py-3 flex items-center justify-between gap-4">
              <div className="flex items-center gap-4 lg:gap-6">
                <Link href="/" className="font-semibold text-lg">ChapterGen</Link>
                <nav className="text-sm hidden md:flex items-center gap-4 lg:gap-6">
                  <Link className="hover:underline transition-colors" href="/">Home</Link>
                  <Link className="hover:underline transition-colors" href="/app">App</Link>
                  <Link className="hover:underline transition-colors" href="/pricing">Pricing</Link>
                  <Link className="hover:underline transition-colors" href="/jobs">Jobs</Link>
                  <Link className="hover:underline transition-colors" href="/videos">Videos</Link>
                </nav>
              </div>
              <HeaderAuth />
            </div>
          </header>
          <main>{children}</main>
          <footer className="border-t">
            <div className="max-w-6xl mx-auto px-4 py-6 text-xs text-gray-500 flex flex-wrap items-center justify-between gap-3">
              <span>© {new Date().getFullYear()} ChapterGen</span>
              <div className="flex items-center gap-3">
                <Link className="hover:underline" href="/jobs">Recent jobs</Link>
                <Link className="hover:underline" href="/videos">Recent videos</Link>
                <Link className="hover:underline" href="/pricing">Pricing</Link>
                <a className="hover:underline" href="https://github.com" target="_blank" rel="noreferrer">GitHub</a>
              </div>
            </div>
          </footer>
        </body>
      </html>
    </ClerkProvider>
  );
}
