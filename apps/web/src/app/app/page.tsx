"use client";
import { use<PERSON><PERSON>back, useMemo, useState, useEffect, useRef } from "react";
import { useUser } from "@clerk/nextjs";
import { formatChaptersForYouTube } from "@/lib/format";
import { extractYouTubeVideoId } from "@/lib/url";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Card } from "@/components/ui/card";
import { ProcessingProgress } from "@/components/processing-progress";
import { ResultsDashboard } from "@/components/results-dashboard";
import { OnboardingModal } from "@/components/onboarding-modal";
import { ProgressiveOptions } from "@/components/progressive-options";
import { usePreferences } from "@/hooks/use-preferences";
import Link from "next/link";

type ApiJob = {
  job_id: string;
  video_id: string;
  status: string;
  estimated_minutes: number;
  billing_preview_minutes: number;
  minutes_billed?: number;
  chapters?: { start: string; end: string; duration_sec: number; title: string; summary?: string }[];
  customer_id?: string;
};

export default function GeneratorApp() {
  const { isLoaded: userLoaded, isSignedIn } = useUser();
  const { preferences, updatePreferences, isLoaded } = usePreferences();
  const [url, setUrl] = useState("");
  const [job, setJob] = useState<ApiJob | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [usage, setUsage] = useState<{ used: number; allowance: number; remaining: number } | null>(null);
  const [userPlan, setUserPlan] = useState<"starter" | "creator" | "pro">("starter");
  const [showOnboarding, setShowOnboarding] = useState(false);
  const pollTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const isUnmountedRef = useRef(false);

  const videoId = useMemo(() => extractYouTubeVideoId(url) || "", [url]);

  // Check for first visit and show onboarding
  useEffect(() => {
    if (isLoaded && !preferences.onboardingCompleted) {
      setShowOnboarding(true);
    }
  }, [isLoaded, preferences.onboardingCompleted]);

  // Cleanup timers on unmount to avoid memory leaks
  useEffect(() => {
    return () => {
      isUnmountedRef.current = true;
      if (pollTimerRef.current) {
        clearTimeout(pollTimerRef.current);
        pollTimerRef.current = null;
      }
    };
  }, []);

  const refreshUsage = useCallback(async () => {
    try {
      const res = await fetch("/api/usage", { cache: "no-store" });

      if (!res.ok) {
        if (res.status === 401) {
          setError("Session expired. Please sign in again.");
          localStorage.removeItem("authToken");
          localStorage.removeItem("user");
          window.location.href = "/signin";
          return;
        }
        throw new Error("Failed to load usage");
      }

      const data = await res.json();
      setUsage({ used: data.used, allowance: data.allowance, remaining: data.remaining });
      setUserPlan(data.plan || "starter");
    } catch (e: any) {
      setError(e.message || "Failed to load usage");
    }
  }, []);

  const submit = useCallback(async () => {
    setError(null);
    setLoading(true);
    try {
      await refreshUsage();
      const res = await fetch("/api/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url,
          options: {
            chapter_density: preferences.chapterDensity,
            title_style: preferences.titleStyle,
            include_summaries: preferences.includeSummaries
          }
        }),
      });
      if (!res.ok) {
        const errJson = await res.json().catch(() => ({}));
        if (res.status === 401) throw new Error("Session expired. Please sign in again.");
        if (res.status === 402) throw new Error(errJson?.message || "Quota exceeded");
        if (res.status === 413) throw new Error("Video too long for your plan limits");
        if (res.status === 429) {
          const retryAfter = Number(res.headers.get("Retry-After") || "0");
          const seconds = Math.max(1, Math.floor(retryAfter));
          throw new Error(`Rate limited. Try again in ~${seconds}s`);
        }
        throw new Error(errJson?.error || `Request failed (${res.status})`);
      }
      const data: ApiJob = await res.json();
      setJob(data);
      const pollStart = Date.now();
      let delayMs = 500; // start at 0.5s
      const maxDelayMs = 5000; // cap at 5s
      const maxElapsedMs = 2 * 60 * 1000; // stop after 2 minutes
      const poll = async () => {
        if (!data.job_id || isUnmountedRef.current) return;
        const r = await fetch(`/api/jobs/${data.job_id}`);
        if (!r.ok) {
          if (r.status === 429) {
            const ra = Number(r.headers.get("Retry-After") || "1") * 1000;
            delayMs = Math.max(delayMs, isFinite(ra) ? ra : delayMs);
          }
        } else {
          const j: ApiJob = await r.json();
          setJob(j);
          if (j.status === "completed") {
            await refreshUsage();
            return;
          }
        }
        const elapsed = Date.now() - pollStart;
        if (elapsed >= maxElapsedMs) return;
        // exponential backoff with jitter
        delayMs = Math.min(maxDelayMs, Math.floor(delayMs * 1.5));
        const jitter = Math.floor(Math.random() * 200);
        pollTimerRef.current = setTimeout(poll, delayMs + jitter);
      };
      poll();
    } catch (e: any) {
      setError(e.message || "Failed to submit");
    } finally {
      setLoading(false);
    }
  }, [url, preferences.chapterDensity, preferences.titleStyle, preferences.includeSummaries, refreshUsage]);

  if (!userLoaded) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center">
        <div className="animate-pulse text-center">
          <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-96 mx-auto"></div>
        </div>
      </div>
    );
  }

  if (!isSignedIn) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold mb-4">Please Sign In</h1>
          <p className="text-gray-600 mb-6">You need to be signed in to use the chapter generator.</p>
          <Button asChild>
            <Link href="/sign-in">Sign In</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[var(--background)]">
      <section className="border-b bg-gradient-to-b from-white/20 to-transparent dark:from-white/5">
        <div className="max-w-6xl mx-auto px-4 py-6 sm:py-10 lg:py-14 grid gap-6 lg:grid-cols-[1.6fr_1fr]">
          <Card className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 mb-4">
              <Input
                placeholder="https://www.youtube.com/watch?v=VIDEO_ID"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                className="flex-1"
                aria-label="YouTube video URL"
                inputMode="url"
                autoCorrect="off"
                autoCapitalize="off"
              />
              <Button
                onClick={submit}
                disabled={loading || !videoId}
                className="w-full sm:w-auto sm:min-w-[120px]"
                size="lg"
                title={!videoId ? "Enter a valid YouTube URL to enable" : undefined}
              >
                {loading ? "Processing…" : "Generate"}
              </Button>
            </div>
            {!videoId && url && <div className="text-xs text-red-600 mb-2">Enter a valid YouTube video URL.</div>}
            {usage && (
              <div className="text-xs text-gray-600 mb-3 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                <span>Usage: {usage.used}/{usage.allowance} min · Remaining {usage.remaining}</span>
                <div className="flex items-center gap-2">
                  <span className="capitalize font-medium">{userPlan} Plan</span>
                  <Link href="/pricing" className="text-blue-600 hover:underline">
                    Upgrade
                  </Link>
                </div>
              </div>
            )}
            <ProgressiveOptions
              density={preferences.chapterDensity}
              titleStyle={preferences.titleStyle}
              includeSummaries={preferences.includeSummaries}
              onDensityChange={(value) => updatePreferences({ chapterDensity: value })}
              onTitleStyleChange={(value) => updatePreferences({ titleStyle: value })}
              onIncludeSummariesChange={(value) => updatePreferences({ includeSummaries: value })}
            />
            {error && <div className="mt-3 text-red-700 bg-red-50 border border-red-200 rounded p-2 text-sm">{error}</div>}
          </Card>
          <Card className="p-4 sm:p-6 min-h-[200px] lg:min-h-[300px]">
            <h3 className="font-medium mb-3 text-sm">Preview</h3>
            {videoId ? (
              <div className="aspect-video rounded overflow-hidden border bg-black/5">
                <iframe
                  key={videoId}
                  className="w-full h-full"
                  src={`https://www.youtube.com/embed/${videoId}`}
                  title="YouTube video preview"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                  allowFullScreen
                />
              </div>
            ) : (
              <div className="flex items-center justify-center h-32 lg:h-48 text-center">
                <p className="text-sm text-gray-500">Paste a YouTube URL above to see the video preview</p>
              </div>
            )}
          </Card>
        </div>
      </section>

      <section className="max-w-6xl mx-auto px-4 py-8">
        {job && (
          <>
            {/* Show processing progress for active jobs */}
            {(job.status === "processing" || job.status === "pending") && (
              <ProcessingProgress
                jobId={job.job_id}
                status={job.status}
                estimatedMinutes={job.estimated_minutes}
                className="mb-6"
              />
            )}

            {/* Show enhanced results dashboard for completed jobs */}
            {job.status === "completed" && job.chapters && (
              <ResultsDashboard
                jobId={job.job_id}
                videoId={job.video_id}
                chapters={job.chapters}
                minutesBilled={job.minutes_billed}
                customerId={job.customer_id}
              />
            )}

            {/* Show error state */}
            {job.status === "failed" && (
              <Card className="p-4 border-red-200 bg-red-50 dark:bg-red-900/20">
                <div className="text-red-700 dark:text-red-300">
                  <h3 className="font-medium mb-2">Processing Failed</h3>
                  <p className="text-sm">
                    Job <span className="font-mono">{job.job_id}</span> failed to complete.
                    Please try again or contact support if the issue persists.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-3"
                    onClick={() => setJob(null)}
                  >
                    Try Again
                  </Button>
                </div>
              </Card>
            )}
          </>
        )}
      </section>

      {/* Onboarding Modal */}
      <OnboardingModal
        isOpen={showOnboarding}
        onClose={() => setShowOnboarding(false)}
        onComplete={() => {
          updatePreferences({ onboardingCompleted: true });
        }}
      />
    </div>
  );
}
