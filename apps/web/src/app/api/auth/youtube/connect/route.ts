/**
 * YouTube OAuth Connect Route - Minimal Test Version
 */

import { NextRequest } from "next/server";

export async function GET(req: NextRequest) {
  return Response.json({
    success: true,
    message: "GET YouTube connect route working",
    timestamp: new Date().toISOString()
  });
}

export async function POST(req: NextRequest) {
  return Response.json({
    success: true,
    message: "POST YouTube connect route working",
    timestamp: new Date().toISOString()
  });
}
