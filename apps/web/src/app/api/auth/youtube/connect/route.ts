/**
 * YouTube OAuth Connect Route
 * Generates OAuth URL for users to connect their YouTube account
 */

import { NextRequest } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { userYouTubeIntegration } from "@/lib/user-youtube-integration";

export async function GET(req: NextRequest) {
  try {
    const { userId } = auth();
    
    if (!userId) {
      return Response.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    console.log('🔐 Generating YouTube OAuth URL for user:', userId);
    
    const result = await userYouTubeIntegration.generateConnectUrl();
    
    if (!result.success) {
      return Response.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return Response.json({
      success: true,
      authUrl: result.url,
      message: "Redirect user to this URL to connect YouTube account"
    });
  } catch (error: any) {
    console.error('❌ YouTube connect error:', error);
    return Response.json(
      { error: error.message || "Failed to generate OAuth URL" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const { userId } = auth();
    
    if (!userId) {
      return Response.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check current connection status
    const isConnected = await userYouTubeIntegration.isUserConnected();
    
    return Response.json({
      success: true,
      connected: isConnected,
      userId,
      message: isConnected ? "YouTube account is connected" : "YouTube account not connected"
    });
  } catch (error: any) {
    console.error('❌ YouTube status check error:', error);
    return Response.json(
      { error: error.message || "Failed to check connection status" },
      { status: 500 }
    );
  }
}
