import { NextRequest } from "next/server";
import { transcribeYouTubeWithGroq } from "@/lib/transcribe";
import { extractYouTubeVideoId } from "@/lib/url";
import { fetchYouTubeAudioAsFile } from "@/lib/audio";

export const runtime = "nodejs";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { url, test_type } = body;

    if (!url) {
      return Response.json(
        { error: "YouTube URL is required" },
        { status: 400 }
      );
    }

    const videoId = extractYouTubeVideoId(url);
    if (!videoId) {
      return Response.json(
        { error: "Invalid YouTube URL" },
        { status: 400 }
      );
    }

    console.log(`🧪 Testing transcription for video: ${videoId}`);

    switch (test_type) {
      case "audio_fetch":
        return await testAudioFetch(url);
      
      case "transcription":
        return await testTranscription(videoId);
      
      case "full_pipeline":
        return await testFullPipeline(url, videoId);
      
      default:
        return Response.json(
          { error: "Invalid test_type. Use: audio_fetch, transcription, or full_pipeline" },
          { status: 400 }
        );
    }
  } catch (error: any) {
    console.error("❌ Test error:", error);
    return Response.json(
      { error: error.message || "Test failed" },
      { status: 500 }
    );
  }
}

async function testAudioFetch(url: string) {
  console.log("🎧 Testing audio fetch...");
  const startTime = Date.now();
  
  try {
    const audioFile = await fetchYouTubeAudioAsFile(url);
    const duration = Date.now() - startTime;
    
    return Response.json({
      success: true,
      test_type: "audio_fetch",
      result: {
        filename: audioFile.name,
        type: audioFile.type,
        size_bytes: audioFile.size,
        size_mb: Math.round(audioFile.size / (1024 * 1024) * 100) / 100,
        fetch_duration_ms: duration
      }
    });
  } catch (error: any) {
    return Response.json({
      success: false,
      test_type: "audio_fetch",
      error: error.message
    }, { status: 500 });
  }
}

async function testTranscription(videoId: string) {
  console.log("🤖 Testing transcription...");
  
  if (!process.env.GROQ_API_KEY) {
    return Response.json({
      success: false,
      test_type: "transcription",
      error: "GROQ_API_KEY not configured"
    }, { status: 500 });
  }

  const startTime = Date.now();
  
  try {
    const result = await transcribeYouTubeWithGroq(videoId);
    const duration = Date.now() - startTime;
    
    if (!result) {
      return Response.json({
        success: false,
        test_type: "transcription",
        error: "Transcription returned null"
      }, { status: 500 });
    }

    return Response.json({
      success: true,
      test_type: "transcription",
      result: {
        text_length: result.text?.length || 0,
        segments_count: result.segments?.length || 0,
        language: result.language,
        transcription_duration_ms: duration,
        first_segment: result.segments?.[0] || null,
        last_segment: result.segments?.[result.segments?.length - 1] || null
      }
    });
  } catch (error: any) {
    return Response.json({
      success: false,
      test_type: "transcription",
      error: error.message
    }, { status: 500 });
  }
}

async function testFullPipeline(url: string, videoId: string) {
  console.log("🔄 Testing full pipeline...");
  const startTime = Date.now();
  const steps: any[] = [];

  try {
    // Step 1: Audio fetch
    console.log("Step 1: Fetching audio...");
    const audioStartTime = Date.now();
    const audioFile = await fetchYouTubeAudioAsFile(url);
    const audioFetchDuration = Date.now() - audioStartTime;

    steps.push({
      step: "audio_fetch",
      success: true,
      duration_ms: audioFetchDuration,
      result: {
        filename: audioFile.name,
        type: audioFile.type,
        size_bytes: audioFile.size,
        size_mb: Math.round(audioFile.size / (1024 * 1024) * 100) / 100
      }
    });

    // Step 2: Transcription
    console.log("Step 2: Transcribing...");
    if (!process.env.GROQ_API_KEY) {
      throw new Error("GROQ_API_KEY not configured");
    }

    const transcriptionStartTime = Date.now();
    const transcriptionResult = await transcribeYouTubeWithGroq(videoId);
    const transcriptionDuration = Date.now() - transcriptionStartTime;

    if (!transcriptionResult) {
      throw new Error("Transcription returned null");
    }

    steps.push({
      step: "transcription",
      success: true,
      duration_ms: transcriptionDuration,
      result: {
        text_length: transcriptionResult.text?.length || 0,
        segments_count: transcriptionResult.segments?.length || 0,
        language: transcriptionResult.language
      }
    });

    const totalDuration = Date.now() - startTime;

    return Response.json({
      success: true,
      test_type: "full_pipeline",
      total_duration_ms: totalDuration,
      steps,
      summary: {
        video_id: videoId,
        audio_size_mb: Math.round(audioFile.size / (1024 * 1024) * 100) / 100,
        transcription_segments: transcriptionResult.segments?.length || 0,
        detected_language: transcriptionResult.language
      }
    });

  } catch (error: any) {
    const totalDuration = Date.now() - startTime;

    return Response.json({
      success: false,
      test_type: "full_pipeline",
      total_duration_ms: totalDuration,
      steps,
      error: error.message
    }, { status: 500 });
  }
}
