import { NextRequest } from "next/server";

import {
  saveJob,
  getJob,
  updateJob<PERSON>tatus,
  completeJob
} from "@/lib/storage";
import { JobRecord } from "@/lib/types";
import { extractYouTubeVideoId } from "@/lib/url";
import { getIdempotentJobId, makeIdempotencyKey, rememberIdempotency } from "@/lib/idempotency";
import { auth } from "@clerk/nextjs/server";
import { MAX_SINGLE_VIDEO_MINUTES, minutesFromSeconds, mockLookupVideoDurationSeconds } from "@/lib/limits";
import { canConsume, recordUsage } from "@/lib/billing";
import { postUsageRecord } from "@/lib/stripe";
import { publish, drainQueue } from "@/lib/bus";
import { segmentByLength, computeChapterCount, generateChaptersWithOpenAI, enhanceChaptersWithOpenAI } from "@/lib/chapters";
import { fetchYouTubeDurationSeconds, fetchYou<PERSON>ube<PERSON>ap<PERSON>, fetchYouTubeSnippet } from "@/lib/youtube";
import { broadcastJobCompleted } from "@/lib/realtime";
// import { transcribeWithGroqFromUrl } from "@/lib/transcribe";
import { fetchYouTubeAudioAsFile } from "@/lib/audio";
import { transcribeYouTubeWithGroq } from "@/lib/transcribe";
import { transcribeWithUserAuth } from "@/lib/user-youtube-integration";
import { getCachedTranscript, setCachedTranscript } from "@/lib/transcriptStore";
import { saveChapters as persistChapters } from "@/lib/chaptersStore";
import { saveVideo as persistVideo } from "@/lib/videosStore";
import { rateLimit, rateLimitCheck, getClientIP, RATE_LIMITS, createRateLimitResponse } from "@/lib/rateLimit";
import { logger } from "@/lib/logger";

export const runtime = "nodejs";

type GenerateRequestBody = {
  url: string;
  options?: {
    chapter_density?: "auto" | "sparse" | "dense";
    language_hint?: string;
    title_style?: "concise" | "descriptive";
    include_summaries?: boolean;
  };
};

function withHeaders(body: any, status = 200, requestId?: string) {
  const headers: Record<string, string> = { "Cache-Control": "no-store" };
  if (requestId) headers["X-Request-Id"] = requestId;
  return Response.json(body, { status, headers });
}

export async function POST(req: NextRequest) {
  try {
    const requestId = crypto.randomUUID();
    // Rate limiting - IP-based
    const clientIP = getClientIP(req.headers);
    const ipRateLimit = await rateLimitCheck(`generate:ip:${clientIP}`, RATE_LIMITS.GENERATE_API);

    if (!ipRateLimit.allowed) {
      logger.warn("Rate limit exceeded (IP)", { requestId, clientIP });
      return createRateLimitResponse(ipRateLimit.resetTime);
    }

    const body = (await req.json()) as GenerateRequestBody;
    if (!body?.url || typeof body.url !== "string") {
      return withHeaders({ error: "Invalid request: 'url' is required" }, 400, requestId);
    }

    // Validate and canonicalize URL (extract video_id)
    const videoId = extractYouTubeVideoId(body.url);
    if (!videoId) {
      return withHeaders({ error: "Could not extract video_id from URL" }, 400, requestId);
    }

    // Check authentication with Clerk
    const { userId } = await auth();
    if (!userId) {
      return withHeaders({ error: "Authentication required" }, 401, requestId);
    }

    // Rate limiting - User-based
    const userRateLimit = await rateLimitCheck(`generate:user:${userId}`, RATE_LIMITS.USER_GENERATE);

    if (!userRateLimit.allowed) {
      logger.warn("Rate limit exceeded (user)", { requestId, userId });
      return createRateLimitResponse(userRateLimit.resetTime);
    }

    // Idempotency: Use header or derive from (video_id, options)
    const headerIdem = req.headers.get("idempotency-key") || undefined;
    const optionsFingerprint = JSON.stringify(body.options || {});
    const generatedKey = makeIdempotencyKey([videoId, optionsFingerprint]);
    const idemKey = headerIdem || generatedKey;
    const existingJobId = getIdempotentJobId(idemKey);
    if (existingJobId) {
      const existing = getJob(existingJobId);
      if (existing) {
        logger.info("Idempotent replay: returning existing job", { requestId, jobId: existingJobId });
        return withHeaders(existing, 202, requestId);
      }
    }

    // Length gate: try real duration via YouTube API, fallback to mock
    const realSec = await fetchYouTubeDurationSeconds(videoId);
    const durationSec = realSec ?? mockLookupVideoDurationSeconds(videoId);
    const durationMin = minutesFromSeconds(durationSec);
    if (durationMin > MAX_SINGLE_VIDEO_MINUTES) {
      return withHeaders({ error: "Video too long" }, 413, requestId);
    }

    // If plan allowance insufficient, block with 402 for now (demo behavior)
    if (!canConsume(userId, "starter", durationMin)) {
      return withHeaders(
        {
          error: "Quota exceeded",
          code: "QUOTA_EXCEEDED",
          minutes_needed: durationMin,
          remaining_minutes: 0,
          plan: "starter",
          message:
            "Your monthly allowance is insufficient for this video. Upgrade plan or wait for reset.",
        },
        402,
        requestId
      );
    }

    // Create job record and persist to in-memory store
    const jobId = `job_${Math.random().toString(36).slice(2, 10)}`;
    const estimatedMinutes = durationMin;
    const nowIso = new Date().toISOString();

    const record: JobRecord = {
      job_id: jobId,
      video_id: videoId,
      status: "queued",
      estimated_minutes: estimatedMinutes,
      billing_preview_minutes: estimatedMinutes,
      created_at: nowIso,
      customer_id: userId,
    };

    saveJob(record);
    rememberIdempotency(idemKey, jobId);
    // Save video metadata
    try { await persistVideo({ video_id: videoId, duration_sec: durationSec, created_at: nowIso, customer_id: userId }); } catch {}
    publish({
      type: "VideoSubmitted",
      payload: {
        jobId,
        videoId,
        estimatedMinutes,
        optionsJson: optionsFingerprint,
      },
    });
    // Auto-drain in dev to simulate orchestration without separate call
    queueMicrotask(() => {
      void drainQueue();
    });
    // Simulate background processing: mark processing, then complete
    queueMicrotask(() => {
      updateJobStatus(jobId, "processing");
      setTimeout(async () => {
        let demoChapters;

        // Groq transcription preferred when GROQ_API_KEY is available
        const ytUrl = body.url;
        if (ytUrl && process.env.GROQ_API_KEY) {
          const cachedMaybe = getCachedTranscript(videoId);
          const cached = (cachedMaybe && typeof (cachedMaybe as any).then === "function") ? await (cachedMaybe as any) : cachedMaybe;
          if (cached?.segments?.length) {
            const target = computeChapterCount(durationMin, body.options?.chapter_density || "auto");
            // Use LLM for chapter generation with cached transcript
            console.log('🤖 Using LLM for chapter generation with cached transcript...');
            const snippet = await fetchYouTubeSnippet(videoId);
            const aiChapters = await generateChaptersWithOpenAI(cached.segments as any, {
              titleStyle: body.options?.title_style || 'concise',
              targetCount: target,
              context: snippet || undefined,
            });
            if (aiChapters) {
              console.log('✅ LLM generated chapters:', aiChapters.map(c => c.title));
              demoChapters = aiChapters;
            } else {
              console.log('❌ LLM chapter generation failed with cached transcript');
              throw new Error('LLM chapter generation failed');
            }
          }
        }
        if (!demoChapters && ytUrl && process.env.GROQ_API_KEY) {
          try {
            console.log('🚀 Using enhanced transcription service...');

            // Try enhanced transcription with user auth first
            let tr = null;
            try {
              const enhancedResult = await transcribeWithUserAuth(ytUrl, {
                preferredMethod: 'auto',
                fallbackToCaptions: true,
                language: body.options?.language_hint
              });

              if (enhancedResult.success) {
                console.log(`✅ Enhanced transcription successful via ${enhancedResult.method}`);
                tr = {
                  text: enhancedResult.text,
                  segments: enhancedResult.segments,
                  language: enhancedResult.language
                };
              } else {
                console.log('⚠️ Enhanced transcription failed, falling back to standard Groq');
                throw new Error(enhancedResult.error || 'Enhanced transcription failed');
              }
            } catch (enhancedError) {
              console.log('⚠️ Enhanced transcription error, using fallback:', enhancedError);
              // Fallback to standard Groq transcription
              tr = await transcribeYouTubeWithGroq(videoId);
            }

            console.log('🤖 Transcription response:', tr ? 'Success' : 'Failed');
            const segments = tr?.segments?.map(s => ({ start: s.start, end: s.end, text: s.text }));
            if (segments?.length) {
              setCachedTranscript(videoId, segments, tr?.language);
              const target = computeChapterCount(durationMin, body.options?.chapter_density || "auto");
              console.log('🤖 Using transcription for chapter generation...');
              const snippet = await fetchYouTubeSnippet(videoId);
              const aiChapters = await generateChaptersWithOpenAI(segments as any, {
                titleStyle: body.options?.title_style || 'concise',
                targetCount: target,
                context: snippet || undefined,
              });
              if (aiChapters) {
                console.log('✅ LLM generated chapters from transcript:', aiChapters.map(c => c.title));
                demoChapters = aiChapters;
              }
            }
          } catch (error) {
            console.log('❌ Transcription failed:', error);
          }
        }
        
        // Captions fallback if Groq not available or failed
        if (!demoChapters) {
          try {
            const caps = await fetchYouTubeCaptions(videoId);
            if (caps?.length) {
              const target = computeChapterCount(durationMin, body.options?.chapter_density || "auto");
              const snippet = await fetchYouTubeSnippet(videoId);
              const aiChapters = await generateChaptersWithOpenAI(caps as any, {
                titleStyle: body.options?.title_style || 'concise',
                targetCount: target,
                context: snippet || undefined,
              });
              if (aiChapters) {
                demoChapters = aiChapters;
              }
            }
          } catch {}
        }
        if (!demoChapters) {
          // Pass video snippet context to LLM for better titles
          const snippet = await fetchYouTubeSnippet(videoId);
          console.log('🔧 No transcript available, generating basic chapters for LLM enhancement');
          const basicChapters = segmentByLength(durationSec, body.options?.chapter_density || "auto");

          // Always use LLM to enhance chapters - whichever provider key is set
          if (process.env.OPENROUTER_API_KEY || process.env.OPENAI_API_KEY) {
            console.log('🤖 Using LLM to generate high-quality chapter titles...');
            try {
              const enhancedChapters = await enhanceChaptersWithOpenAI(basicChapters, {
                titleStyle: body.options?.title_style || 'concise',
                videoId: videoId,
                duration: durationSec
              });
              if (enhancedChapters) {
                console.log('✅ LLM generated high-quality chapter titles');
                demoChapters = enhancedChapters;
              } else {
                console.log('❌ LLM enhancement failed, no fallback - returning error');
                throw new Error('LLM chapter generation failed');
              }
            } catch (error) {
              console.log('❌ LLM enhancement failed:', error);
              throw new Error('LLM chapter generation failed');
            }
          } else {
            console.log('❌ No OpenRouter API key - LLM chapter generation not available');
            throw new Error('LLM chapter generation not configured');
          }
        }

        console.log('📋 Final LLM-generated chapter titles:', demoChapters.map(c => c.title));
        if (body.options?.include_summaries) {
          demoChapters = demoChapters.map((c, i) => ({
            ...c,
            summary: `Overview of ${c.title.toLowerCase()} (segment ${i + 1}).`,
          }));
        }
        if (canConsume(userId, "starter", estimatedMinutes)) {
          recordUsage(userId, estimatedMinutes);
          void postUsageRecord({
            customer_id: userId,
            minutes: estimatedMinutes,
            timestamp: Date.now(),
            job_id: jobId,
          });
        }
        completeJob(jobId, demoChapters, estimatedMinutes);
        // Persist chapters separately for listing/querying
        try { await persistChapters(videoId, demoChapters); } catch {}
        // Best-effort real-time push to connected clients
        try {
          const latest = getJob(jobId);
          if (latest) broadcastJobCompleted(latest as any);
        } catch {}
      }, 1500);
    });

    return withHeaders(record, 202, requestId);
  } catch (err) {
    logger.error('Generate API error', { error: (err as any)?.message });
    return Response.json({ error: "Bad Request" }, { status: 400, headers: { "Cache-Control": "no-store" } });
  }
}



