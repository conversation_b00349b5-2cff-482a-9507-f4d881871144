/**
 * YouTube OAuth Disconnect Route
 * Allows users to disconnect their YouTube account
 */

import { NextRequest } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { userYouTubeIntegration } from "@/lib/user-youtube-integration";

export async function POST(req: NextRequest) {
  try {
    const { userId } = auth();
    const user = await currentUser();
    const actualUserId = userId || user?.id;

    if (!actualUserId) {
      return Response.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    console.log('🔐 Disconnecting YouTube account for user:', actualUserId);
    
    const result = await userYouTubeIntegration.disconnectUser();
    
    if (!result.success) {
      return Response.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return Response.json({
      success: true,
      message: "YouTube account disconnected successfully"
    });
  } catch (error: any) {
    console.error('❌ YouTube disconnect error:', error);
    return Response.json(
      { error: error.message || "Failed to disconnect YouTube account" },
      { status: 500 }
    );
  }
}
