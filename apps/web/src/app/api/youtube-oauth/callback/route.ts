/**
 * YouTube OAuth Callback Route
 * Handles the OAuth callback from YouTube
 */

import { NextRequest } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { userYouTubeIntegration } from "@/lib/user-youtube-integration";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const code = url.searchParams.get('code');
    const state = url.searchParams.get('state');
    const error = url.searchParams.get('error');

    // Handle OAuth errors
    if (error) {
      console.log('❌ YouTube OAuth error:', error);
      return Response.redirect(
        new URL(`/app?youtube_error=${encodeURIComponent(error)}`, req.url)
      );
    }

    if (!code || !state) {
      return Response.redirect(
        new URL('/app?youtube_error=missing_parameters', req.url)
      );
    }

    console.log('🔐 Processing YouTube OAuth callback...');
    
    const result = await userYouTubeIntegration.handleCallback(code, state);
    
    if (!result.success) {
      console.log('❌ YouTube OAuth callback failed:', result.error);
      return Response.redirect(
        new URL(`/app?youtube_error=${encodeURIComponent(result.error || 'callback_failed')}`, req.url)
      );
    }

    console.log('✅ YouTube OAuth connection successful');
    
    // Redirect back to app with success message
    return Response.redirect(
      new URL('/app?youtube_connected=true', req.url)
    );
  } catch (error: any) {
    console.error('❌ YouTube callback error:', error);
    return Response.redirect(
      new URL(`/app?youtube_error=${encodeURIComponent('internal_error')}`, req.url)
    );
  }
}
