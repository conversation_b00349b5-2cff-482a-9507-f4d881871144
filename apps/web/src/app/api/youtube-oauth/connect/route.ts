/**
 * YouTube OAuth Connect Route
 * Generates OAuth URL for users to connect their YouTube account
 */

import { NextRequest } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";

export async function GET(req: NextRequest) {
  try {
    // Try multiple methods to get user authentication
    const { userId } = auth();
    const user = await currentUser();

    console.log('🔐 GET /api/youtube-oauth/connect - auth() userId:', userId);
    console.log('🔐 GET /api/youtube-oauth/connect - currentUser() id:', user?.id);

    const actualUserId = userId || user?.id;

    if (!actualUserId) {
      console.log('❌ No userId found in auth() or currentUser()');
      return Response.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    console.log('🔐 Generating YouTube OAuth URL for user:', actualUserId);

    // For now, return a mock OAuth URL
    return Response.json({
      success: true,
      authUrl: "https://accounts.google.com/oauth2/auth?mock=true",
      userId: actualUserId,
      message: "Mock OAuth URL (integration in progress)"
    });
  } catch (error: any) {
    console.error('❌ YouTube connect error:', error);
    return Response.json(
      { error: error.message || "Failed to generate OAuth URL" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Try multiple methods to get user authentication
    const { userId } = auth();
    const user = await currentUser();

    console.log('🔐 POST /api/youtube-oauth/connect - auth() userId:', userId);
    console.log('🔐 POST /api/youtube-oauth/connect - currentUser() id:', user?.id);

    const actualUserId = userId || user?.id;

    if (!actualUserId) {
      console.log('❌ No userId found in auth() or currentUser()');
      return Response.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    console.log('🔐 Checking YouTube connection status for user:', actualUserId);

    // For now, return mock connection status
    const isConnected = false;

    return Response.json({
      success: true,
      connected: isConnected,
      userId: actualUserId,
      message: "Mock connection status (integration in progress)"
    });
  } catch (error: any) {
    console.error('❌ YouTube status check error:', error);
    return Response.json(
      { error: error.message || "Failed to check connection status" },
      { status: 500 }
    );
  }
}
