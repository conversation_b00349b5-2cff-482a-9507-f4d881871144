import { auth } from "@clerk/nextjs/server";
import { getUsageSummary } from "@/lib/billing";
import { listUsageRecords } from "@/lib/stripe";
import { getBonusRemaining } from "@/lib/billing";

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return Response.json(
        { error: "Authentication required" },
        { status: 401, headers: { "Cache-Control": "no-store" } }
      );
    }

    const summary = getUsageSummary(userId, "starter");
    const records = listUsageRecords();
    return Response.json(
      { ...summary, records, bonus_remaining: getBonusRemaining(userId) }, 
      { status: 200, headers: { "Cache-Control": "no-store" } }
    );
  } catch (error) {
    console.error('Usage API error:', error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500, headers: { "Cache-Control": "no-store" } }
    );
  }
}


