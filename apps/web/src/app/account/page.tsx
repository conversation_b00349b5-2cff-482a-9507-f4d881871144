"use client";
import { useEffect, useState } from "react";
import Link from "next/link";
import { useUser } from "@clerk/nextjs";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { UsageCalculator } from "@/components/usage-calculator";
import { YouTubeConnection } from "@/components/youtube-connection";
import {
  CreditCard,
  TrendingUp,
  Clock,
  AlertTriangle,
  CheckCircle,
  Settings,
  Download
} from "lucide-react";

type Usage = {
  customer_id: string;
  plan: "starter" | "creator" | "pro";
  used: number;
  allowance: number;
  remaining: number;
};

export default function AccountPage() {
  const { isLoaded, isSignedIn, user } = useUser();
  const [usage, setUsage] = useState<Usage | null>(null);
  const [records, setRecords] = useState<Array<{ minutes: number; timestamp: number; job_id: string }>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isLoaded || !isSignedIn) return;
    
    (async () => {
      try {
        const res = await fetch("/api/usage", { cache: "no-store" });

        if (!res.ok) {
          setError(res.status === 401 ? "Please sign in to view your account" : "Failed to load usage data");
          setLoading(false);
          return;
        }

        const data = await res.json();
        setUsage({ customer_id: data.customer_id, plan: data.plan, used: data.used, allowance: data.allowance, remaining: data.remaining });
        setRecords(Array.isArray(data.records) ? data.records : []);
      } catch (e: any) {
        setError(e.message || "Failed to load usage");
      } finally {
        setLoading(false);
      }
    })();
  }, [isLoaded, isSignedIn]);

  if (!isLoaded) {
    return (
      <div className="max-w-6xl mx-auto p-4 sm:p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-64 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-96 mb-8"></div>
          <div className="grid gap-6 md:grid-cols-2">
            <div className="h-48 bg-gray-200 rounded"></div>
            <div className="h-48 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!isSignedIn) {
    return (
      <div className="max-w-6xl mx-auto p-4 sm:p-6">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold mb-4">Please Sign In</h1>
          <p className="text-gray-600 mb-6">You need to be signed in to view your account.</p>
          <Button asChild>
            <Link href="/sign-in">Sign In</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-2">Account Dashboard</h1>
        <p className="text-gray-600">Manage your subscription and monitor usage</p>
      </div>

      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {error && (
        <Card className="p-6 border-red-200 bg-red-50 dark:bg-red-900/20">
          <div className="flex items-center gap-2 text-red-700 dark:text-red-300">
            <AlertTriangle className="h-5 w-5" />
            <p>{error}</p>
          </div>
        </Card>
      )}

      {usage && (
        <div className="space-y-6">
          {/* Quick Stats */}
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                  <CreditCard className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Current Plan</p>
                  <p className="font-semibold capitalize">{usage.plan}</p>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                  <Clock className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Used This Month</p>
                  <p className="font-semibold">{usage.used} min</p>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                  <TrendingUp className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Remaining</p>
                  <p className={`font-semibold ${usage.remaining <= 10 ? 'text-orange-600' : 'text-green-600'}`}>
                    {usage.remaining} min
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                  <CheckCircle className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Usage</p>
                  <p className="font-semibold">{Math.round((usage.used / usage.allowance) * 100)}%</p>
                </div>
              </div>
            </Card>
          </div>

          <div className="grid gap-6 lg:grid-cols-2">
            {/* Subscription Details */}
            <Card className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <Settings className="h-5 w-5 text-gray-600" />
                <h2 className="text-xl font-semibold">Subscription Details</h2>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Monthly Usage</span>
                    <span>{Math.round((usage.used / usage.allowance) * 100)}%</span>
                  </div>
                  <Progress value={(usage.used / usage.allowance) * 100} className="h-2" />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>{usage.used}/{usage.allowance} minutes</span>
                    <span>{usage.remaining} remaining</span>
                  </div>
                </div>

                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Plan</span>
                    <span className="font-medium capitalize">{usage.plan}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Monthly Allowance</span>
                    <span className="font-medium">{usage.allowance} minutes</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Email</span>
                    <span className="text-sm">{user?.emailAddresses[0]?.emailAddress}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">User ID</span>
                    <span className="font-mono text-xs">{user?.id}</span>
                  </div>
                </div>

                {usage.remaining <= 10 && (
                  <div className="p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 rounded">
                    <div className="flex items-center gap-2 text-orange-800 dark:text-orange-200 text-sm">
                      <AlertTriangle className="h-4 w-4" />
                      <span><strong>Low on minutes!</strong> Consider upgrading to avoid overage charges.</span>
                    </div>
                  </div>
                )}

                <div className="pt-4 border-t space-y-2">
                  <Button asChild className="w-full">
                    <Link href="/pricing">
                      {usage.plan === 'starter' ? 'Upgrade Plan' : 'Change Plan'}
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full">
                    <CreditCard className="h-4 w-4 mr-2" />
                    Manage Billing
                  </Button>
                </div>
              </div>
            </Card>

            {/* Usage Calculator */}
            <UsageCalculator
              currentUsage={usage}
              currentPlan={usage.plan}
            />
          </div>

          {/* YouTube Integration */}
          <div className="grid gap-6 lg:grid-cols-2">
            <YouTubeConnection />

            {/* Placeholder for future integrations */}
            <Card className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <Settings className="h-5 w-5 text-gray-600" />
                <h2 className="text-xl font-semibold">More Integrations</h2>
              </div>
              <p className="text-gray-600 mb-4">
                Connect additional services to enhance your transcription experience.
              </p>
              <div className="space-y-2">
                <div className="p-3 border rounded-lg bg-gray-50 dark:bg-gray-800/50">
                  <p className="text-sm text-gray-500">More integrations coming soon...</p>
                </div>
              </div>
            </Card>
          </div>
        </div>
      )}

          {/* Usage History */}
          {usage && (
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-gray-600" />
                  <h2 className="text-xl font-semibold">Usage History</h2>
                </div>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>

              {records.length === 0 ? (
                <div className="text-center py-12">
                  <Clock className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No usage records yet.</p>
                  <p className="text-sm text-gray-400 mt-1">
                    Start generating chapters to see your usage history here.
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-3 font-medium">Date</th>
                        <th className="text-left p-3 font-medium">Minutes Used</th>
                        <th className="text-left p-3 font-medium">Job ID</th>
                        <th className="text-left p-3 font-medium">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {records.map((r, i) => (
                        <tr key={`${r.job_id}-${i}`} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800/50">
                          <td className="p-3">{new Date(r.timestamp).toLocaleDateString()}</td>
                          <td className="p-3 font-medium">{r.minutes} min</td>
                          <td className="p-3 font-mono text-xs text-gray-600">{r.job_id}</td>
                          <td className="p-3">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300">
                              Completed
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </Card>
          )}
    </div>
  );
}


