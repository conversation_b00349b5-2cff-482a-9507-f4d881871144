"use client";

import { useUser } from "@clerk/nextjs";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function DebugAuthPage() {
  const { user, isLoaded, isSignedIn } = useUser();
  const [testResults, setTestResults] = useState<any[]>([]);

  const runTest = async (testName: string, url: string, method: string = 'GET') => {
    const startTime = Date.now();
    try {
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const duration = Date.now() - startTime;
      const data = await response.text();
      
      setTestResults(prev => [...prev, {
        testName,
        url,
        method,
        status: response.status,
        duration,
        success: response.ok,
        data: data.substring(0, 200) + (data.length > 200 ? '...' : ''),
      }]);
    } catch (error: any) {
      const duration = Date.now() - startTime;
      setTestResults(prev => [...prev, {
        testName,
        url,
        method,
        status: 'ERROR',
        duration,
        success: false,
        data: error.message,
      }]);
    }
  };

  const clearResults = () => setTestResults([]);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Authentication Debug Page</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">Clerk Auth Status:</h3>
            <div className="space-y-1 text-sm">
              <p>Is Loaded: {isLoaded ? '✅' : '❌'}</p>
              <p>Is Signed In: {isSignedIn ? '✅' : '❌'}</p>
              <p>User ID: {user?.id || 'None'}</p>
              <p>Email: {user?.emailAddresses?.[0]?.emailAddress || 'None'}</p>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="font-semibold">API Tests:</h3>
            <div className="flex flex-wrap gap-2">
              <Button 
                onClick={() => runTest('Usage API', '/api/usage')}
                size="sm"
              >
                Test /api/usage
              </Button>
              <Button 
                onClick={() => runTest('YouTube Status', '/api/auth/youtube/connect', 'POST')}
                size="sm"
              >
                Test YouTube Status
              </Button>
              <Button 
                onClick={() => runTest('YouTube Connect', '/api/auth/youtube/connect', 'GET')}
                size="sm"
              >
                Test YouTube Connect
              </Button>
              <Button 
                onClick={clearResults}
                variant="outline"
                size="sm"
              >
                Clear Results
              </Button>
            </div>
          </div>

          {testResults.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">Test Results:</h3>
              <div className="space-y-2">
                {testResults.map((result, index) => (
                  <div 
                    key={index} 
                    className={`p-3 rounded border ${result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium">{result.testName}</span>
                      <span className={`text-sm ${result.success ? 'text-green-600' : 'text-red-600'}`}>
                        {result.status} ({result.duration}ms)
                      </span>
                    </div>
                    <div className="text-xs text-gray-600">
                      {result.method} {result.url}
                    </div>
                    <div className="text-xs mt-1 font-mono bg-gray-100 p-1 rounded">
                      {result.data}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
